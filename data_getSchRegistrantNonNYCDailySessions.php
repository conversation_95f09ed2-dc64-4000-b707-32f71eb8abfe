<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$SessionDate = $_GET['SessionDate'];
 

    $query = "	  SELECT 
                       a.Id as SessionId,
                       DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                       DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS SessionDate,     
                        a.ServiceDate AS ServiceDateSort,
                       DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                       a.StartTime as StartTimeSort, 
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      TotalHours,
                      
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      WeekDay,
                      TextColor,
                      BackgroundColor,
                      SchoolName,
                     SessionTypeId,
                     case SessionTypeId
                         when '0' then 'Therapy'
                         when '1' then 'Evaluation'
                         when '3' then 'Notes Writing' 
                         when '4' then 'Admin' 
                         when '5' then 'Sick Time'
                         when '6' then 'Lactation Time'  
                         when '7' then 'Jury Duty'  

                       end as SessionTypeDesc,
                        CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
                      DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate       
                 FROM  WeeklyServices a,   ScheduleStatuses g, SchSchools d, Users e 
                        WHERE a.RegistrantId = '{$RegistrantId}'
                        AND   a.ServiceDate = '{$SessionDate}'
                        AND   a.ScheduleStatusId > 6
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   d.DistrictId = 34 
                        AND   a.UserId = e.UserId  
                 ORDER BY ServiceDateSort, StartTimeSort

                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

