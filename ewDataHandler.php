<?php
// Remove PEAR DB dependency
// require_once('DB.php');

/*===========================================================
// Class - data_handler*/
class dataHandler {
         
    var $connection;

    function dataHandler() {
        include('db_login.php');
        
        // Replace PEAR DB connection with mysqli
        $this->connection = new mysqli($db_host, $db_username, $db_password, $db_database);
        
        // Check connection
        if ($this->connection->connect_error) {
            die("Could not connect to the database: <br />" . $this->connection->connect_error);
        }
        
        // Set charset
        $this->connection->set_charset('utf8');
    }

    /* Close Open Connection
    //=======================*/			
    function disconnectDB() {
        $this->connection->close();
    }

    // Helper function to execute queries and return all results as associative array
    private function getAll($query, $params = [], $types = '') {
        $stmt = $this->connection->prepare($query);
        
        if (!$stmt) {
            die("Could not prepare statement: " . $this->connection->error);
        }
        
        // Bind parameters if provided
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        if (!$result) {
            die("Could not query the database:<br />$query " . $this->connection->error);
        }
        
        $rows = [];
        while ($row = $result->fetch_assoc()) {
            $rows[] = $row;
        }
        
        $stmt->close();
        return $rows;
    }
    
    // Helper function to execute a query without returning results
    private function execute($query, $params = [], $types = '') {
        $stmt = $this->connection->prepare($query);
        
        if (!$stmt) {
            die("Could not prepare statement: " . $this->connection->error);
        }
        
        // Bind parameters if provided
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $result = $stmt->execute();
        
        if (!$result) {
            die("Could not execute query:<br />$query " . $this->connection->error);
        }
        
        $stmt->close();
        return $result;
    }

    // Get States Listing
    //=======================			
    function getStates() {
        $query = "SELECT State, StateName FROM States";
        return $this->getAll($query);
    }
    
    // Get Dashboard Side Navigation Data
    //=======================			
    function getDashboardSideNavigation() {
        $query = "SELECT * FROM DashboardSideNavigation WHERE Status = 1 ORDER BY id";
        return $this->getAll($query);
    }
    
    // Get Client Side Navigation Data
    //=======================			
    function getClientSideNavigation() {
        $query = "SELECT * FROM ClientSideNavigationNew WHERE Status = 1 ORDER BY id";
        return $this->getAll($query);
    }
    
    // Get Clients Listing
    //=======================			
    function getClients() {
        $query = "SELECT Id as id, Id, ClientName, SearchId 
                  FROM Clients 
                  WHERE SchoolFL = '1'
                  ORDER BY ClientName";
        return $this->getAll($query);
    }

    // Get Selected Client's general Info
    //=======================			
    function getSelClient($ClientId) {
        $query = "SELECT a.Id as id, 
                    a.Id, 
                    SearchId,
                    ClientStatusId,
                    ClientName,
                    StreetAddress1,
                    StreetAddress2,
                    City,
                    State,
                    ZipCode,
                    OfficePhone,
                    Fax,
                    RecruitorId,  
                    
                    ( select CONCAT( trim( b.FirstName) , ' ', trim( b.LastName)) 
                            from Users b
                            Where RecruitorId = b.UserId ) as RecruitorName,  
                    CoordinatorId,
                    
                    ( select CONCAT( trim( c.FirstName) , ' ', trim( c.LastName)) 
                            from Users c
                            Where CoordinatorId = c.UserId ) as CoordinatorName,  
                    a.UserId  
                    FROM Clients a  
                    WHERE Id = ?";
        
        return $this->getAll($query, [$ClientId], 's');
    }

    // Set Selected Client's general Info
    //=======================			
    function setSelClient($Id, $SearchId, $ClientStatusId, $ClientName, $StreetAddress1, 
                          $StreetAddress2, $City, $State, $ZipCode, $OfficePhone,
                          $Fax, $RecruitorId, $CoordinatorId, $UserId) {
        if(is_numeric($Id)) {
            $query = "UPDATE Clients
                      SET ClientStatusId = ?,  
                          SearchId = ?,
                          ClientName = ?,  
                          StreetAddress1 = ?,  
                          StreetAddress2 = ?,  
                          City = ?,  
                          State = ?,
                          ZipCode = ?,
                          OfficePhone = ?,
                          Fax = ?,
                          RecruitorId = ?,
                          CoordinatorId = ?,
                          UserId = ?,
                          TransDate = NOW()
                      WHERE Id = ?";
            
            return $this->execute($query, [
                $ClientStatusId, $SearchId, $ClientName, $StreetAddress1, $StreetAddress2,
                $City, $State, $ZipCode, $OfficePhone, $Fax, $RecruitorId, $CoordinatorId,
                $UserId, $Id
            ], 'ssssssssssssss');
        } else {
            $query = "INSERT INTO Clients (
                          SearchId, ClientStatusId, ClientName, StreetAddress1, StreetAddress2,
                          City, State, ZipCode, OfficePhone, Fax, RecruitorId, CoordinatorId,
                          UserId, TransDate
                      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            return $this->execute($query, [
                $SearchId, $ClientStatusId, $ClientName, $StreetAddress1, $StreetAddress2,
                $City, $State, $ZipCode, $OfficePhone, $Fax, $RecruitorId, $CoordinatorId, $UserId
            ], 'sssssssssssss');
        }
    }
    
    // Get Client Messages
    //=======================			
    function getClientMessages($ClientId) {
        $query = "SELECT Id as MsgId,
                    Msg,
                    HighPriority,
                    CASE HighPriority 
                        WHEN '1' THEN 'High'
                        ELSE 'Normal'
                    END AS HighPriorityLast,
                    CASE HighPriority 
                        WHEN '1' THEN 'red'
                        ELSE 'black'
                    END AS PriorityColor,
                    ClientId,
                    CONCAT(trim(b.FirstName), ' ', trim(b.LastName)) AS UserName,
                    DATE_FORMAT(a.TransDate, '%m-%d-%Y %r') as TransDate
                FROM ClientMessages a, Users b  
                WHERE ClientId = ?
                AND a.UserId = b.UserId 
                ORDER BY a.TransDate DESC";
        
        return $this->getAll($query, [$ClientId], 's');
    }
    
    // Get Registrants Listing
    //=======================			
    function getRegistrants($Statuses, $SearchId) {
        // For IN clauses, we need to handle differently
        $query = "SELECT a.Id as id,  
                    TypeId,
                    ExtId,
                    SearchId,			
                    CONCAT(trim(LastName), ', ', trim(FirstName), ' (', RegistrantTypeDesc, ')') as RegistrantName
                FROM Registrants a, RegistrantTypes
                WHERE Typeid = RegistrantTypes.Id 
                AND StatusID in " . $Statuses . "
                AND SearchId LIKE ?
                ORDER BY LastName, FirstName";
        
        return $this->getAll($query, ['%' . $SearchId . '%'], 's');
    }
    
    // Get the List of Client's Mandatory Credentialing Items
    //=========================
    function getCredItemsClientMandatorySelected($ClientId, $ServiceTypeId) {
        // For stored procedures, we need special handling
        $query = "CALL proc_getCredItemsClientMandatorySelected1(?, ?)";
        
        return $this->getAll($query, [$ClientId, $ServiceTypeId], 'ss');
    }

    // Set Selected Registrant's general Info
    //=======================
    function setSelRegistrant($Id,
                            $StatusId,
                            $SearchId,
                            $ExtId,
                            $TerminationType,
                            $TerminationReason,
                            $TerminationDate,
                            $BirthDate,
                            $HireDate,
                            $Gender,

                            $Race,
                            $CheckType,
                            $W4Status,
                            $Exemptions,

                            $TypeId,
                            $FirstName,
                            $LastName,
                            $MiddleInitial,
                            $StreetAddress1,
                            $StreetAddress2,
                            $City,
                            $State,
                            $ZipCode,
                            $MobilePhone,
                            $HomePhone,
                            $Fax,
                            $Email,
                            $Availability,
                            $HospitalExp,
                            $Shifts,
                            $NextDayPay,
                            $PerDiem,
                            $ThrnWeekContract,
                            $NewGraduate,
                            $ForeignTrained,
                            $HrTypeId,
                            $SupervisorId,
                            $TherapyPayRate,
                            $EvalPayRate,
                            $SupervisionPayRate,
                            $NotesWritingPayRate,
                            $AdminPayRate,
                            $SickTimePayRate,
                            $LactationTimePayRate,
                            $JuryDutyPayRate,
                            $PayrollId,
                            $UserId) {

        if(is_numeric($Id)) {
            $query = "UPDATE Registrants
                    SET StatusId = ?,
                        SearchId = ?,
                        ExtId = ?,
                        TerminationType = ?,
                        TerminationReason = ?,
                        TerminationDate = ?,
                        BirthDate = ?,
                        HireDate = ?,
                        Gender = ?,
                        Race = ?,
                        CheckType = ?,
                        W4Status = ?,
                        Exemptions = ?,
                        TypeId = ?,
                        FirstName = ?,
                        LastName = ?,
                        MiddleInitial = ?,
                        StreetAddress1 = ?,
                        StreetAddress2 = ?,
                        City = ?,
                        State = ?,
                        ZipCode = ?,
                        MobilePhone = ?,
                        HomePhone = ?,
                        Fax = ?,
                        Email = ?,
                        Availability = ?,
                        HospitalExp = ?,
                        Shifts = ?,
                        NextDayPay = ?,
                        PerDiem = ?,
                        ThrnWeekContract = ?,
                        NewGraduate = ?,
                        ForeignTrained = ?,
                        HrTypeId = ?,
                        SupervisorId = ?,
                        TherapyPayRate = ?,
                        EvalPayRate = ?,
                        SupervisionPayRate = ?,
                        NotesWritingPayRate = ?,
                        AdminPayRate = ?,
                        SickTimePayRate = ?,
                        LactationTimePayRate = ?,
                        JuryDutyPayRate = ?,
                        PayrollId = ?,
                        UserId = ?,
                        TransDate = NOW()
                    WHERE Id = ?";

            return $this->execute($query, [
                $StatusId, $SearchId, $ExtId, $TerminationType, $TerminationReason,
                $TerminationDate, $BirthDate, $HireDate, $Gender, $Race,
                $CheckType, $W4Status, $Exemptions, $TypeId, $FirstName,
                $LastName, $MiddleInitial, $StreetAddress1, $StreetAddress2,
                $City, $State, $ZipCode, $MobilePhone, $HomePhone,
                $Fax, $Email, $Availability, $HospitalExp, $Shifts,
                $NextDayPay, $PerDiem, $ThrnWeekContract, $NewGraduate,
                $ForeignTrained, $HrTypeId, $SupervisorId, $TherapyPayRate,
                $EvalPayRate, $SupervisionPayRate, $NotesWritingPayRate,
                $AdminPayRate, $SickTimePayRate, $LactationTimePayRate,
                $JuryDutyPayRate, $PayrollId, $UserId, $Id
            ], 'ssssssssssssssssssssssssssssssssssssssssssssss');

        } else {
            $query = "INSERT INTO Registrants (
                        StatusId, SearchId, ExtId, TerminationType, TerminationReason,
                        TerminationDate, BirthDate, HireDate, Gender, Race,
                        CheckType, W4Status, Exemptions, TypeId, FirstName,
                        LastName, MiddleInitial, StreetAddress1, StreetAddress2,
                        City, State, ZipCode, MobilePhone, HomePhone,
                        Fax, Email, Availability, HospitalExp, Shifts,
                        NextDayPay, PerDiem, ThrnWeekContract, NewGraduate,
                        ForeignTrained, HrTypeId, SupervisorId, TherapyPayRate,
                        EvalPayRate, SupervisionPayRate, NotesWritingPayRate,
                        AdminPayRate, SickTimePayRate, LactationTimePayRate,
                        JuryDutyPayRate, PayrollId, EntryDate, UserId, TransDate
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW())";

            return $this->execute($query, [
                $StatusId, $SearchId, $ExtId, $TerminationType, $TerminationReason,
                $TerminationDate, $BirthDate, $HireDate, $Gender, $Race,
                $CheckType, $W4Status, $Exemptions, $TypeId, $FirstName,
                $LastName, $MiddleInitial, $StreetAddress1, $StreetAddress2,
                $City, $State, $ZipCode, $MobilePhone, $HomePhone,
                $Fax, $Email, $Availability, $HospitalExp, $Shifts,
                $NextDayPay, $PerDiem, $ThrnWeekContract, $NewGraduate,
                $ForeignTrained, $HrTypeId, $SupervisorId, $TherapyPayRate,
                $EvalPayRate, $SupervisionPayRate, $NotesWritingPayRate,
                $AdminPayRate, $SickTimePayRate, $LactationTimePayRate,
                $JuryDutyPayRate, $PayrollId, $UserId
            ], 'sssssssssssssssssssssssssssssssssssssssssssss');
        }
    }

			// get Selected Registrant's Additional Info 
			//=======================================	
			function getRegistrantAddtionalInfo($RegistrantId) {
                       $query ="SELECT 	
								COALESCE(MobilePhone,'') as MobilePhone,
								COALESCE(HomePhone,'') as HomePhone,
								CASE Email 
									WHEN '' THEN 'No'
								ELSE 'Yes'
								END AS HasEmail,
								(SELECT RegistrantTypeDesc FROM RegistrantTypes
											WHERE Typeid  = RegistrantTypes.Id) as  RegistrantTypeDesc,	

								CASE COALESCE(LastPayDate, 0)  
									WHEN '0' THEN ''
									ELSE DATE_FORMAT( LastPayDate, '%m-%d-%Y' )
								END as LastPayDate,
								(SELECT count(*) from RegistrantCredItems d, CredentialingItems b
										where Registrants.Id = d.RegistrantId
										and d.CredItemId = b.Id
										and (!(b.CredItemCategory = 2 and d.StatusId = '0'))
										/*AND  d.StatusId in (1,2)*/	
										AND ComplianceLevelId != 2 ) as CredItemsNonCompliant,    
										
								(SELECT count(*) from RegistrantCredItems d
										where Registrants.Id = d.RegistrantId
										/*AND d.StatusId in (1,2)*/	
										AND ComplianceLevelId = 2 ) as CredItemsCompliant,

								(SELECT count(*) from RegistrantCredItems d
										where Registrants.Id = d.RegistrantId
										/*AND d.StatusId in (1,2)*/	
										AND DATEDIFF( ExpirationDate, CURDATE( ) ) between 0 and 30) as CredItemsExp30Days
							FROM Registrants 
							WHERE Id = ?";

                        return $this->getAll($query, [$RegistrantId], 's');
			

			}
			
			// Get Registrants  Listing
            //=======================			
			function getRegistrants($Statuses, $SearchId) {
			
                        $query = "SELECT a.Id as id,  
											TypeId,
											ExtId,
											SearchId,			
											CONCAT( trim( LastName) , ', ', trim(FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
										FROM Registrants a, RegistrantTypes
										WHERE Typeid  = RegistrantTypes.Id 
										AND StatusID in " . $Statuses . "
										AND SearchId like ?
										order by LastName, FirstName";

						return $this->getAll($query, ['%' . $SearchId . '%'], 's');

			}

			// Get Registrant Statuses Listing
            //=======================			
			function getRegistrantStatuses() {
			
                        $query = "SELECT Id as id,
										RegistrantStatusDesc  										 
									FROM RegistrantStatuses";
									
						
						return $this->getAll($query);
			}			
			
			// Get Registrant Document Types Listing
            //=======================			
			function getRegistrantDocumentTypes() {
			
                        $query = "SELECT Id as id,
										CredItemDesc as DocumentTypeDesc  										 
									FROM CredentialingItems
									Order By CredItemDesc";
									
						
						return $this->getAll($query);
			}			

			// Get Registrant Documents Listing
            //=======================================			
			function getRegistrantDocuments($RegistrantId) {
			
                        $query = "SELECT 	a.Id as id, 
											DocumentTypeId, 
											CredItemDesc as DocumentTypeDesc,
											DocumentName, 
											StoredName, 
											a.UserId, 
											CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
											DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										FROM RegistrantDocuments a, CredentialingItems b, Users c 
										WHERE a.DocumentTypeId = b.Id
										AND a.UserId = c.UserId
										AND RegistrantId = ?";

						return $this->getAll($query, [$RegistrantId], 's');
			}	

			// Get Specialties Listing for a given Registrant  
            //=======================			
			function getRegistrantSpecialties ($RegistrantId) {
			
                        $query ="Select SpecialtyId as id, SpecialtyDesc 
							from RegistrantAttchedSpecialties, Specialties 
						where SpecialtyId = Specialties.Id and
						  RegistrantId  = ?
						  Order By SpecialtyDesc";

                        return $this->getAll($query, [$RegistrantId], 's');
						
			}


			//  Update Registrant's Specialties     
            //=======================			
			function setRegistrantSpecialties($RegistrantId,
										$Specialties,			
										$UserId) {
					
 
							
 
                    // Delete All Existing Specialties for an Registrant
					//===============================================
					$query = "DELETE FROM RegistrantAttchedSpecialties
								WHERE RegistrantId = ?";

					$this->execute($query, [$RegistrantId], 's');

					// Insert newaly Selected Specialties for an Registrant
					//===============================================					

					foreach ($Specialties as $SpecialtyId) {
								
							$Sel_Speciality = $SpecialtyId['id'];
							
												
						$query = "INSERT INTO RegistrantAttchedSpecialties
						   (RegistrantId, SpecialtyId, UserId, TransDate)
						VALUES (?, ?, ?, NOW())";

						$this->execute($query, [$RegistrantId, $Sel_Speciality, $UserId], 'sss');
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/* Get Registrant Credentialing Items  
            //=============================	*/		
			function getRegistrantCredItems($RegistrantId) {
			
                        $query ="SELECT a.Id as RegistrantCredItemTransId,
										RegistrantId,
										a.CredItemId,
										a.StatusId,
										b.CredItemCategory,
										CredItemType,
										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										CredItemDesc,
										a.CredItemStatus,
										CredItemStatusColor,
										CredItemStatusBGColor,
										CredItemStatusDesc,
										ComplianceLevelId,
										'' as CondItemSelId,
										''as CredItemCondGrpId,
										
										CASE  ComplianceLevelId 
											WHEN '1' THEN 'Non-Compliant'
										ELSE 'Compliant'
										END AS ComplianceLevelDesc,
										COALESCE((SELECT StoredName FROM RegistrantDocuments h
											WHERE a.RegistrantId = h.RegistrantId
											AND   a.CredItemId = h.DocumentTypeId),'') as StoredDocName,	
										
										
										COALESCE(b.Comments,'') as Comments,
										COALESCE(a.Results,'') as Results,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										COALESCE(DATE_FORMAT( a.OrigDocumentDate, '%m-%d-%Y' ),'')  as OrigDocumentDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	RegistrantCredItems a,
									CredentialingItems b,
									CredentialingItemStatuses g,
									Users e 
						WHERE 	a.CredItemId = b.Id 
								AND a.UserId = e.UserId
								AND a.CredItemStatus = g.CredItemStatus
								AND RegistrantId = ?
							ORDER BY CredItemDesc";

                        return $this->getAll($query, [$RegistrantId], 's');
			}

          /* Set Registrant Credentialing Items  
          //=======================	*/		
			function setRegistrantCredItems($RegistrantId,
											$CredItemId, 
											$CredItemStatus,
											$ComplianceLevelId,
											$OrigDocumentDate,
											$ExpirationDate,
											$Results,
											$UserId) {
			
                        $query ="Update RegistrantCredItems 
								Set CredItemStatus = '{$CredItemStatus}',
								    ComplianceLevelId = '{$ComplianceLevelId}', 
									PrevExpirationDate = ExpirationDate,
									OrigDocumentDate = '{$OrigDocumentDate}',
									ExpirationDate = '{$ExpirationDate}',
									Results = '{$Results}',
								    UserId = '{$UserId}',
									TransDate = now()
							WHERE 	RegistrantId = '{$RegistrantId}' 
									AND CredItemId = ?";

                        return $this->getAll($query, [$RegistrantId, $CredItemId], 'ss');
						
			}	
			/* Get Registrant Cred Item Messages
            //=======================	*/		
			function getRegistrantCredItemMsgs($RegistrantCredItemTransId) {
			
                        $query ="SELECT a.Id as id ,
						        a.Id as Id ,
								Msg,
								HighPriority,
								CommunicationModeId,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDateSort,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM RegistrantCredItemMessages a, Users b, CommunicationModes c  
								WHERE RegistrantCredItemTransId =  '{$RegistrantCredItemTransId}'	
								AND a.UserId = b.UserId 
								AND CommunicationModeId = c.Id
								Order By  TransDateSort Desc";

                        return $this->getAll($query);
			}

			/* Set  Registrant Cred Item Messages
            //=======================	*/		
			function setRegistrantCredItemMsgs(	$RegistrantCredItemTransId, 
												$Msg,
												$HighPriority,	  
												$UserId)
			
			{ 
				 
                       $query = "INSERT INTO RegistrantCredItemMessages (
															RegistrantCredItemTransId,
															Msg,
															HighPriority,
															UserId,
															TransDate
														) VALUES (?, ?, ?, ?, NOW())";


                        return $this->execute($query, [$RegistrantCredItemTransId, $Msg, $HighPriority, $UserId], 'ssss');
	 		}				
			
			/* Set Switch Registrant Conditional Crededntialing Item
            //======================= */ 			
			function setSwitchRegistrantCondItem(	$RegistrantCredItemTransId, 
													$CredItemCondGrpId,
													$CondItemSelId,
													$UserId) {
			
 
							
                        $query = "CALL proc_SwitchSchoolRegistrantCredItem (?, ?, ?, ?)";
                        return $this->getAll($query, [$RegistrantCredItemTransId, $CredItemCondGrpId, $CondItemSelId, $UserId], 'ssss');
						
			}	

			//=======================================			
			/* Get Registrant's Client Approvals    */
           //========================================			
			function getRegistrantClientsApprovals($RegistrantId) {
			
                        $query ="SELECT a.ClientId AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										c.ClientName,
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
						FROM 	ClientApprovedRegistrantsNonRest a, 
							Users b,
							Clients c
							
						WHERE a.UserId = b.UserId
						AND a.RegistrantId = '{$RegistrantId}' 
						AND a.ClientId = c.Id
					 
						
					Order By c.ClientName ";

                        return $this->getAll($query);
			}		

          // Get Client Weekly Schedules
            //=======================			
			function getClientWklySchedules($ClientId, 
											$PayrollWeek, 
											$ServiceDate,			
											$InclClientUnitId,
											$ServiceTypeId	
											) 
			{
			
 
							
                        $query = "CALL proc_ClientWklySchedules (?, ?, ?, ?, ?)";
                        return $this->getAll($query, [$ClientId, $PayrollWeek, $ServiceDate, $InclClientUnitId, $ServiceTypeId], 'sssss');
						
			}				

			// Get Schedule Messages
            //=======================			
		 	function getClientWklySchedulesMsgs($ScheduleId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDate1,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM WeeklyServicesMessages a, Users b  
								WHERE ScheduleId = '{$ScheduleId}' 
								AND a.UserId = b.UserId 
								Order By  TransDate1 Desc";

                        return $this->getAll($query);
			}			

			// Get Client Weekly Schedule "Summary" Info
            //=======================			
			function getClientWklyScheduleSummary ($ClientId,	  
											$PayrollWeek) {
						
						$query = "CALL proc_ClientWklyScheduleSummary (?, ?)";
 						return $this->getAll($query, [$ClientId, $PayrollWeek], 'ss');
						//return $query; 
						
			}	
			//=======================
			// Get Service Time
            //=======================			
			function getServiceTime() {
													 
                       $query ="SELECT 	ServTime as id,
										ServTime,
                                        ServTimeDesc  					   
							FROM DailyServiceTimes
						Order by TimeSorter";

                        return $this->getAll($query);
						//return $query;	
			}				
		 	
			//=======================
			// Get Schedule Statuses Listing
            //=======================			 
			function getScheduleStatuses() {
			
                        $query = "SELECT Id as id,
										ScheduleStatusDesc  										 
									FROM ScheduleStatuses
									WHERE OpenStatusFlag = 0";

						return $this->getAll($query);
			}

			// Get Schedule Shifts Listing
            //=======================			
			function getScheduleShifts() {
			
                        $query = "SELECT Id as id, 	
										 Id as ScheduleShiftId,
									     ScheduleShiftDesc ,
                                         StartTime,
										 EndTime			
									FROM ScheduleShifts
									ORDER BY Id";

						return $this->getAll($query);
			}			

			// Get Default Shifts Listing
            //=======================			
			function getDefaultShifts() {
			
                        $query = "SELECT Id as id, 	
										 Id as ShiftId,
									     ShiftNameDefault as ShiftName,
                                         StartTime,
										 EndTime,
										 TotalHours		
									FROM DefaultShifts
									ORDER BY Id";

						return $this->getAll($query);
			}			
			
			// Check for Registrant's Duplicate Schedules
            //=======================			
			function getRegistrantDupSchedules($ServiceDate,
											 $StartTime,
											 $EndTime,
										     $RegistrantId) {

						$query ="SELECT count(*) as dup_shift
									FROM  WeeklyServices 
							WHERE  ServiceDate  = ?
							AND  StartTime  >= ?
							AND  EndTime  <= ?
							AND  RegistrantId  = ?";

						return $this->getAll($query, [$ServiceDate, $StartTime, $EndTime, $RegistrantId], 'ssss');
						
			}	

           // Set Client Weekly Schedule
            //=======================			
			function setClientWklySchedules($PayrollWeek,
									$ClientId,
									$ClientUnitId,
									$ScheduleId,
									$ScheduleStatusId,
									$ServiceDate,
									$StartTime,
									$EndTime,
									$TotalHours,
									$WeekDay, 
									$RegistrantId,
									$RegistrantTypeId,
									$ScheduleOrigBy,
									$UserId ) 
					{
				
							$query = "INSERT INTO WeeklyServices (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,
												StartTime,
												EndTime,
												TotalHours,
												WeekDay,
												RegistrantId,
												RegistrantTypeId,
												ScheduleOrigBy,
												UserId,
												TransDate
											) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

					return $this->execute($query, [
						$PayrollWeek, $ClientId, $ClientUnitId, $ScheduleStatusId, $ServiceDate,
						$StartTime, $EndTime, $TotalHours, $WeekDay, $RegistrantId,
						$RegistrantTypeId, $ScheduleOrigBy, $UserId
					], 'sssssssssssss');
			}				

			/* Get Client Weekly Services Messages
            //=======================	*/		
		 	function getClientWklyServicesMsgs($ServiceId) {
			
                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,
								
								CASE HighPriority 
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority 
								
								WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								TransDate as TransDate1,
								a.TransDate as TransDate
							FROM WeeklyServicesMessages a, Users b  
								WHERE ServiceId = '{$ServiceId}' 
								AND a.UserId = b.UserId 
								Order By  TransDate1 Desc";

                        return $this->getAll($query);
						//return $query;
			}				
			
			/* Set Client Weekly Schedule Message */
            /*======================= */			
			function setClientWklyServicesMsg(	$ServiceId,
													$HighPriority,
													$Msg,
													$UserId )  
			{
				
                       $query = "INSERT INTO WeeklyServicesMessages (
								ServiceId,
								HighPriority,
								Msg,
								UserId,
								TransDate
							) VALUES (?, ?, ?, ?, NOW())";

						return $this->execute($query, [$ServiceId, $HighPriority, $Msg, $UserId], 'ssss');
						
			}	

           //=======================			
			function setClientWklyScheduleMsg(	$ScheduleId, 
												$Msg,
												$HighPriority,	  
												$UserId) 	{ 
				 
                       $query = "INSERT INTO WeeklyServicesMessages (
																		ScheduleId,
																		Msg,
																		HighPriority,
																		UserId,
																		TransDate
																	) VALUES (?, ?, ?, ?, NOW())";


                        return $this->execute($query, [$ScheduleId, $Msg, $HighPriority, $UserId], 'ssss');
						//return $query;  
	 		}	

			// Get Client Approved Registrants by Proximity Data
            //=======================			
			function getClientApprRegistrantsByProximity (	$ClientId, 
															$RegistrantTypeId,
															$ServiceDate,
															$StartTime,
															$EndTime ) {
			
  						$query = "CALL proc_RegistrantProximityToClient (?, ?, ?, ?, ?)";
                        return $this->getAll($query, [$ClientId, $RegistrantTypeId, $ServiceDate, $StartTime, $EndTime], 'sssss');
						
			}

			// Set Schedule Shifts Status
            //=======================			
			function setClientWklyScheduleStatus(
								$ScheduleId,  
								$ScheduleStatusId, 
								$RegistrantConfFL, 
								$ClientConfFL, 
								$RegistrantId, 
								$UserId	) {
			
                        $query = "UPDATE WeeklyServices
									SET ScheduleStatusId = ?,
										RegistrantConfFL = ?,
										ClientConfFL = ?,
										RegistrantId = ?,
										UserId = ?,
										TransDate = NOW()
									WHERE Id = ?";

						return $this->execute($query, [$ScheduleStatusId, $RegistrantConfFL, $ClientConfFL, $RegistrantId, $UserId, $ScheduleId], 'ssssss');
						
			}

			// Set Cancelled By Registrant Info
            //==================================================			
			function setCancelledByRegistrant(	$RegistrantId,
												$ScheduleId, 
												$ServiceCancellationReasonId,
												$CancelReason,
												$UserId) 	{ 
				 
                       $query = "INSERT INTO RegistrantServiceCancellations (
																				RegistrantId,
																				ScheduleId,
																				ServiceCancellationReasonId,
																				CancelReason,
																				Userid,
																				TransDate
																			) VALUES (?, ?, ?, ?, ?, NOW())";


                        return $this->execute($query, [$RegistrantId, $ScheduleId, $ServiceCancellationReasonId, $CancelReason, $UserId], 'sssss');
	 		}			

			// Set Cancelled By Client Info
            //=========================================			
			function setCancelledByClient(	$ClientId,
											$ScheduleId, 
											$ServiceCancellationReasonId,
											$CancelReason,
											$UserId) 	{ 
				 
                       $query = "INSERT INTO ClientServiceCancellations (
																		ClientId,
																		ScheduleId,
																		ServiceCancellationReasonId,
																		CancelReason,
																		Userid,
																		TransDate
																	) VALUES (?, ?, ?, ?, ?, NOW())";

                        return $this->execute($query, [$ClientId, $ScheduleId, $ServiceCancellationReasonId, $CancelReason, $UserId], 'sssss');
						//return $query;  
	 		}
			 
			// Set Cancelled By Coordinator Info
            //============================================			
			function setCancelledByCoordinator(	$CoordinatorId,
												$ScheduleId, 
												$ServiceCancellationReasonId,
												$CancelReason,
												$UserId) 	{ 
				 
                       $query = "INSERT INTO CoordinatorServiceCancellations (
																				CoordinatorId,
																				ScheduleId,
																				ServiceCancellationReasonId,
																				CancelReason,
																				Userid,
																				TransDate
																			) VALUES (?, ?, ?, ?, ?, NOW())";

                        return $this->execute($query, [$CoordinatorId, $ScheduleId, $ServiceCancellationReasonId, $CancelReason, $UserId], 'sssss');
						//return $query;  
	 		}
			// Set Confirmed Schedules By Client Info
            //=======================			
			function getConfirmedSchedulesByClient(	$ClientId,
													$StartDate, 
													$EndDate) 	{
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										ScheduleStatusId, 
										ScheduleStatusDesc,
										TextColor,
										BackgroundColor,
										a.ClientId, 
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										PayrollWeek,  
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											ScheduleStatuses g								
										Where 	a.ClientId= '{$ClientId}' 
												AND a.ScheduleStatusId > 6 
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND  ServiceDate between '{$StartDate}' and '{$EndDate}' 
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
											ORDER BY ServiceDate, StartTime ";

                        return $this->getAll($query);
						//return $query;  
	 		}

			// Set Registrant Schedules By Client Info
            //=======================			
			function getRegistrantWklyAvailability(	$RegistrantId,
													$PayrollWeek) 	{
				 
                       $query ="SELECT 	ServiceDate, 
										DAYOFWEEK(ServiceDate) as DayId, 
										WeekDay, 
										ShiftId,
										CASE 
											WHEN (ShiftId ='1' ) THEN CONCAT(WeekDay, 'DayShift')  
												ELSE '0' 
										END as DayShift,
										CASE  
											WHEN (ShiftId ='2' ) THEN CONCAT(WeekDay, 'EveningShift')  
												ELSE '0' 
										END as EveningShift,
										CASE  
											WHEN (ShiftId ='3' ) THEN CONCAT(WeekDay, 'NightShift')  
												ELSE '0' 
										END as NightShift,
										CASE  
											WHEN (ShiftId ='4' ) THEN CONCAT(WeekDay, '12HrsDayShift')  
												ELSE '0' 
										END as 's12HrsDayShift',
										CASE  
											WHEN (ShiftId ='5' ) THEN CONCAT(WeekDay, '12HrsNightShift')  
												ELSE '0' 
										END as 's12HrsNightShift'
										
								FROM RegistrantVerifiedAvailability
								WHERE PayrollWeek = ?
								AND RegistrantId = ?";

                        return $this->getAll($query, [$PayrollWeek, $RegistrantId], 'ss');
						//return $query;  
	 		}
			// Set Registrant Weekly Schedule
            //=======================			
			function setRegistrantWklySchedules($PayrollWeek,
									$ClientId,
									$ClientUnitId,
									$ScheduleId,
									$ScheduleStatusId,
									$ServiceDate,
									$WeekDay, 
									$RegistrantId,
									$RegistrantTypeId,
									$ShiftId,
									$UserId ) 
					{
				
							$query = "INSERT INTO WeeklyServices (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,
												StartTime,
												EndTime,
												TotalHours,
												WeekDay,
												RegistrantId,
												RegistrantTypeId,
												ShiftId,
												RegistrantConfFL,
												UserId,
												TransDate
											) SELECT
												?, ?, ?, ?, ?,
												StartTime, EndTime, TotalHours,
												?, ?, ?, ?, '1', ?, NOW()
											FROM ClientShifts
											WHERE ClientId = ? AND ShiftId = ?";

					return $this->execute($query, [
						$PayrollWeek, $ClientId, $ClientUnitId, $ScheduleStatusId, $ServiceDate,
						$WeekDay, $RegistrantId, $RegistrantTypeId, $ShiftId, $UserId,
						$ClientId, $ShiftId
					], 'ssssssssssss');
				//return $query;			
			}				

			// Set Registrant Weekly Availability
            //=======================			
			function setRegistrantWklyAvailability(	$PayrollWeek,
													$ServiceDate,
													$WeekDay, 
													$RegistrantId,
													$ShiftId,
													$UserId ) 
					{
				
                   // Delete Reggistrant's Existing "Non-Matched" Availability Data
					//========================== =====================
							
					$query = "INSERT INTO RegistrantVerifiedAvailability (
							RegistrantId,
							ServiceDate,
							PayrollWeek,
							WeekDay,
							ShiftId,
							UserId,
							TransDate
						) VALUES (?, ?, ?, ?, ?, ?, NOW())";

					return $this->execute($query, [$RegistrantId, $ServiceDate, $PayrollWeek, $WeekDay, $ShiftId, $UserId], 'ssssss');
				//return $query;			
			}				
			
			// Get Client's Restricted Registrants Info
            //=======================			
			function getClientRestRegistrants($ClientId) {
			
                        $query ="SELECT a.RegistrantId as id,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RestrictType,
										CASE RestrictType
											WHEN '1' THEN 'Clinical'
											WHEN '0' THEN 'Non-Clinical'
										END AS RestrictTypeDesc,
										RestrictReason,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										
							FROM 	ClientRestrictedRegistrants a,
									Registrants b, 
									Users c,
									RegistrantTypes f  
					where 	a.RegistrantId = b.Id 
						AND b.TypeId = f.Id
						AND a.UserId = c.UserId
						AND RestrictStatus = 1
						AND a.ClientId = '{$ClientId}' 
						 Order By b.LastName, b.FirstName";

                        return $this->getAll($query);
			}

			// Get Client's nonRestricted Registrants Info
            //=======================			
			function getClientNonRestRegistrants($ClientId) {
			
                        $query ="SELECT a.RegistrantId as id,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
							FROM ClientApprovedRegistrants a,
								Registrants b, RegistrantTypes f  
					where 	a.RegistrantId = b.Id 
						and b.TypeId = f.Id
						and a.Status = 1
						and a.ClientId = '{$ClientId}' 
						and not exists (Select 1 from ClientRestrictedRegistrants d
                                            Where a.RegistrantId = d.RegistrantId
                                       		AND RestrictStatus = 1	)   
						 Order By b.LastName, b.FirstName";

                        return $this->getAll($query);
			}

			//  Remove  Client's Restriction     
            //=============================================			
			function setRemoveClientRestRegistrant(	$RegistrantId,
													$ClientId,	  
													$UserId) {
 												
						$query = "UPDATE ClientRestrictedRegistrants
							SET RestrictStatus = 2,
								UserId = ?,
								TransDate = NOW()
							WHERE RegistrantId = ?
							AND ClientId = ?
							AND RestrictStatus = 1";

						return $this->execute($query, [$UserId, $RegistrantId, $ClientId], 'sss');
			}	

			//  Update  Client's Restriction Reason    
            //=============================================			
			function setClientRestRegistrantReason(	$RegistrantId,
													$ClientId,	  
													$RestrictType,
													$RestrictReason,			
													$UserId) {
 												
						$query = "INSERT INTO ClientRestrictedRegistrants (
							RegistrantId, ClientId, RestrictType, RestrictReason, UserId, TransDate
						) VALUES (?, ?, ?, ?, ?, NOW())";

						return $this->execute($query, [$RegistrantId, $ClientId, $RestrictType, $RestrictReason, $UserId], 'sssss');
			}				
			
          //===============================================
			// Set Adjust Client Schedule 
            //===============================================			
			function setAdjustClientSchedule(
								$ScheduleId,
								$StartTime,
								$EndTime,
								$LunchHour,
								$TotalHours,
								$ClientUnitId,
								$ServiceTypeId,
								$UserId) 
			{ 
                        $query = "UPDATE WeeklyServices
							SET StartTime = ?,
								EndTime = ?,
								LunchHour = ?,
								TotalHours = ?,
								ClientUnitId = ?,
								ServiceTypeId = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

						return $this->execute($query, [$StartTime, $EndTime, $LunchHour, $TotalHours, $ClientUnitId, $ServiceTypeId, $UserId, $ScheduleId], 'ssssssss');
			}

			/* Get TiemCard Site Navigation Data
            //=======================	*/		
			function getTimeCardSideNavigation() {
			
                        $query = "SELECT *							
						FROM TimeCardNavigation
					ORDER BY id";

                        return $this->getAll($query);
						
			}	

			// Get Registrant Tiem Card Verificaiton Data
            //=======================			
			function getRegistrantTimeCardVerification($RegistrantId, $PayrollWeek) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id AS ScheduleId,
										ScheduleStatusId, 
										ScheduleStatusDesc,
										a.ServiceTypeId,
										ServiceTypeDesc,
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                                        SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,  
										SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
										PayrollWeek,   
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										a.ClientId,
										ClientName,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											Clients d,
											ClientUnits c,
											ServiceTypes h,
											ScheduleStatuses g								
										Where 	a.RegistrantId= '{$RegistrantId}' 
												AND PayrollWeek = '{$PayrollWeek}'
												AND ScheduleStatusId = 7
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.UserId = e.UserId
												AND a.ClientId = d.Id
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
												AND a.ServiceTypeId = h.Id
											ORDER BY ServiceDateSort";

                        return $this->getAll($query);
			}	

			// Get Time Card Transactions (by Client) Data
            //=======================			
			function getClientTimeCardTransactions($ClientId, $PayrollWeek) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id AS ScheduleId,
										ScheduleStatusId, 
										ScheduleStatusDesc,
										a.ServiceTypeId,
										ServiceTypeDesc,
										ServiceDate as ServiceDateSort,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
                                        SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,  
										SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
										PayrollWeek,   
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										a.ClientId,
										ClientName,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											Clients d,
											ClientUnits c,
											ServiceTypes h,
											ScheduleStatuses g								
										Where 	a.ClientId= '{$ClientId}' 
												AND PayrollWeek = '{$PayrollWeek}'
												AND ScheduleStatusId = 7
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.UserId = e.UserId
												AND a.ClientId = d.Id
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
												AND a.ServiceTypeId = h.Id
											ORDER BY ServiceDateSort";

                        return $this->getAll($query);
						//return $query;
			}				

			// Set Verify Time Card 
            //=======================			
			function setRegistrantTimeCardVerification(
								$InclSchedules,
								$ScheduleStatusId,
								$UserId) 
			{ 
                        $query = "UPDATE WeeklyServices
							SET ScheduleStatusId = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE FIND_IN_SET(Id, ?)";

						return $this->execute($query, [$ScheduleStatusId, $UserId, $InclSchedules], 'sss');
			}

            //=====================================		
			// Get TimeCards Upload Data Options
            //=====================================		
			function getRegistrantTimeCardsUploadDataOptions() {
			
                        $query = "SELECT Id as id,
										 UploadDesc,
										 UploadLink			
						FROM RegistrantsTimeCardsDataUpload
					ORDER BY Id";

                        return $this->getAll($query);
						
			}

			// Get Client's Shifts Info
            //=======================			
			function getClientShifts($ClientId) {
			
                        $query ="SELECT a.ShiftId AS id, 
										a.ShiftId,     
										a.ClientId,
										ShiftName, 
										a.Status,
										CASE Status 
											WHEN '1' THEN 'Active'
											ELSE 'Inactive'
										END AS StatusDesc,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime, 
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										LunchHour,
										TotalHours,
										DefaultFL,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM ClientShifts a, Users b 
										WHERE a.UserId = b.UserId
										AND a.ClientId = '{$ClientId}' 
									ORDER BY ShiftId";

                        return $this->getAll($query);
			}	

			//============================================	
			// Set Client Shift Info
            //============================================		
			function setClientShifts ( $form_data )
		
			{

				$ClientId = $form_data->{'ClientId'};
				$ShiftId = $form_data->{'ShiftId'};
				$UserId = $form_data->{'UserId'};
				$Status = $form_data->{'Status'};
				$ShiftName = $form_data->{'ShiftName'};
				$StartTime = $form_data->{'StartTime'};
				$EndTime = $form_data->{'EndTime'};
				$LunchHour = $form_data->{'LunchHour'};
				$TotalHours = $form_data->{'TotalHours'};
				$DefaultFL = $form_data->{'DefaultFL'};

				if ($DefaultFL == '1') {

						$query1 = "UPDATE ClientShifts
							SET DefaultFL = '0'
							WHERE ClientId = ?
							AND DefaultFL = '1'";

                        $this->execute($query1, [$ClientId], 's');
				
		               $query = "UPDATE ClientShifts
									SET Status = ?,
										ShiftName = ?,
										StartTime = ?,
										EndTime = ?,
										LunchHour = ?,
										TotalHours = ?,
										DefaultFL = '1',
										UserId = ?,
										TransDate = NOW()
									WHERE ClientId = ?
									AND ShiftId = ?";

                        $this->execute($query, [$Status, $ShiftName, $StartTime, $EndTime, $LunchHour, $TotalHours, $UserId, $ClientId, $ShiftId], 'sssssssss');
				} else {

	               $query = "UPDATE ClientShifts
								SET Status = ?,
									ShiftName = ?,
									StartTime = ?,
									EndTime = ?,
									LunchHour = ?,
									TotalHours = ?,
									UserId = ?,
									TransDate = NOW()
								WHERE ClientId = ?
								AND ShiftId = ?";

	                        $this->execute($query, [$Status, $ShiftName, $StartTime, $EndTime, $LunchHour, $TotalHours, $UserId, $ClientId, $ShiftId], 'sssssssss');

				}



						return true;
			}				

           //==============================================		
			// Get Conditional Credentialing Items Header  
            //=============================================		
			function getRegistrantCredItemsConditionalHeader($RegistrantId) {
			
                        $query = "SELECT a.Id as id,
										 a.Id as ConditionalItemId, 	
										 a.ConditionalItemDesc,
										 a.TriggerValueDesc,
										 b.ConditionalSwitch				
						FROM CredentialingItemsConditionalHeader a,
						     RegistrantCredentialingItemsConditional b
						WHERE RegistrantId = '{$RegistrantId}'
						AND   a.Id = b.ConditionalItemId
							ORDER BY ConditionalItemDesc";

                        return $this->getAll($query);
						
			}

           //==============================================		
			// Get Conditional Credentialing Items Details  
            //=============================================		
			function getRegistrantCredItemsConditionalDetails($ConditionalItemId, $ConditionalSwitch) {
			
                        $query = "SELECT a.CredItemId , CredItemDesc
									FROM CredentialingItemsConditionalDetails a, CredentialingItems b
								  WHERE ConditionalItemId = '{$ConditionalItemId}'
									AND ConditionalSwitch = ?
									AND a.CredItemId = b.Id";

                        return $this->getAll($query, [$ConditionalSwitch], 's');
						
			}
	
			//==============================================		
			// Set Registrant's Credential Item Status (On/Off) for Optional items ONLY  
            //=============================================		
			function setRegistrantCredItemsStatus($CredItemTransId, $StatusId, $UserId) {
			
                        $query = "UPDATE RegistrantCredItems
									SET StatusId = ?,
									    UserId = ?,
										TransDate = NOW()
							WHERE Id = ?";

                        return $this->execute($query, [$StatusId, $UserId, $CredItemTransId], 'sss');
			}			


			//=====================================================		
			// Set Registrant's New Conditional Credential Items   
            //====================================================		
			function setRegistrantNewConditionalCredItems(	$RegistrantId, 
															$ConditionalItemId, 
															$ConditionalSwitch, 
															$UserId) {
						$query = "CALL proc_setRegistrantNewConditionalCredItems (?, ?, ?, ?)";
 						return $this->execute($query, [$RegistrantId, $ConditionalItemId, $ConditionalSwitch, $UserId], 'ssss');
			}			

			//===============================================
			// Set Adjust Client Schedule 
            //===============================================			
			function setAdjustTimeCard(
								$ScheduleId,
								$StartTime,
								$EndTime,
								$LunchHour,
								$TotalHours,
								$ClientUnitId,
								$ServiceTypeId,
								$UserId) 
			{ 
                        $query = "UPDATE WeeklyServices
							SET StartTime = ?,
								EndTime = ?,
								LunchHour = ?,
								TotalHours = ?,
								ClientUnitId = ?,
								ServiceTypeId = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

						return $this->execute($query, [$StartTime, $EndTime, $LunchHour, $TotalHours, $ClientUnitId, $ServiceTypeId, $UserId, $ScheduleId], 'ssssssss');
			}			

			//==============================			
			// Get Clients Daily Schedules
			//=============================			
			function getClientsDailySchedules( $ServiceDate) {
			
 
							
                        $query = "CALL proc_ClientsDailySchedules (?)";
                        return $this->getAll($query, [$ServiceDate], 's');
						
			}

			//==============================			
			// Get Clients Date Range Schedules
			//=============================			
			function getClientsDateRangeSchedules($FromDate, $ToDate) {
			
 
							
                        $query = "CALL proc_ClientsDateRangeSchedules (?, ?)";
                        return $this->getAll($query, [$FromDate, $ToDate], 'ss');
						
			}

			//============================================
			// Get Confirmed Schedules By Registrant Info
            //============================================			
			function getConfirmedSchedulesByRegistrant(	$RegistrantId,
														$StartDate, 
														$EndDate) 	{
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										ScheduleStatusId, 
										ScheduleStatusDesc,
										TextColor,
										BackgroundColor,
										a.ClientId, 
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										RegistrantTypeDesc, 
										RegistrantTypeId,
										ClientName,
										ClientUnitId,
										UnitName,
									

										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											Clients d,
											ScheduleStatuses g								
										Where 	a.RegistrantId= '{$RegistrantId}' 
												AND ScheduleStatusId > 6
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.ClientId = d.Id
												AND  ServiceDate between '{$StartDate}' and '{$EndDate}' 
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
											ORDER BY ServiceDate, StartTime";

                        return $this->getAll($query);
						//return $query;  
	 		}			

			// Set Registrant Cred Item Email Messages
            //=======================			
			function setEmailCredItemsMsg($RegistrantId, 
												$UserId) 	{ 
				 
                       $query = "INSERT INTO RegistrantCredItemMessages (RegistrantCredItemTransId,
																		Msg,
																		HighPriority,
																		UserId,
																		TransDate)

													SELECT Id,
															'Cred. Item related Email was sent',
															'0',
															?,
															NOW()
													FROM RegistrantCredItems
														WHERE RegistrantId = ?
														AND ComplianceLevelId = 1";

                        return $this->execute($query, [$UserId, $RegistrantId], 'ss');
	 		}		

			//=====================================================
			// Get Registrant's Service Cancellations History Info
            //=====================================================			
			function getRegistrantServiceCancellations($RegistrantId) {
			
                        $query ="SELECT CONCAT( trim( d.LastName ) , ', ', trim( d.FirstName ) , ' (', RegistrantTypeDesc, ')' ) AS RegistrantName,
                                        ClientName,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) as ServiceDate,
										CancelReason,
										COALESCE((SELECT  ServiceCancellationReasonDesc
												FROM ServiceCancellationReasons e
												WHERE  e.id = ServiceCancellationReasonId 
											),'Undefined') as ServiceCancellationReasonDesc,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	RegistrantServiceCancellations a,
                                            Registrants d, 
											RegistrantTypes c,
                                            WeeklyServices f,  
                                            Clients g, 
                                            
											Users b			
									WHERE 	a.RegistrantId = d.Id
											AND Typeid = c.Id
											AND a.UserId = b.UserId
                                            AND a.ScheduleId = f.Id
                                            AND f.ClientId = g.Id  
                                             
                                            AND a.RegistrantId = '{$RegistrantId}' 
                                        ORDER BY a.TransDate DESC";

                        return $this->getAll($query);
			}						

			//==============================			
			// Get Registrant's Monthly Calendar
			//=============================			
			function getRegistrantMonthlyCalendar($RegistrantId, $StartDate) {
			
 
							
                        $query = "CALL proc_RegistrantMontlyCalendar (?, ?)";
                        return $this->getAll($query, [$RegistrantId, $StartDate], 'ss');
						
			}

			/* Get Billing Side Navigation Data
            //=======================	*/		
			function getBillingSideNavigation() {
			
                        $query = "SELECT *							
							FROM BillingSideNavigation
						ORDER BY Id";

                        return $this->getAll($query);
						
			}	
			
			/* Get Service Contracts Data
             ============================	*/		
			function getServiceContracts() {
			
                        $query ="SELECT Id as id,
                                        Id as ContractId,
										StatusId,
										ContractName,
										COALESCE((Select group_concat( ClientName  
                                         SEPARATOR ', ' ) 
											FROM Clients c
											WHERE a.Id = c.ContractId ),'') as  ParticipClients,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContracts a,
									Users b
							WHERE a.UserId = b.UserId
							ORDER BY Id";

                        return $this->getAll($query);
						
			}	

			/* Get Service Contracts (Active) Data
             ============================	*/		
			function getServiceContractsActive() {
			
                        $query ="SELECT Id as id,
                                        Id as ContractId,
										ContractName
							FROM 	ServiceContracts
							WHERE StatusId = '1'
							ORDER BY Id";

                        return $this->getAll($query);
						
			}				
			
			/* Get Admin Side Navigation Data
            //=======================	*/		
			function getAdminSideNavigation() {
			
                        $query = "SELECT *							
							FROM AdminSideNavigation
						ORDER BY Id";

                        return $this->getAll($query);
						
			}	

			//  Update Service Contract Info 
            //=======================			
			function setServiceContract(   
										$ContractId,
										$StatusId,
										$ContractName,
										$UserId ) 
			{
					
					if(is_numeric($ContractId))  {

                         $query = "UPDATE ServiceContracts
							SET StatusId = ?,
								ContractName = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";
						return $this->execute($query, [$StatusId, $ContractName, $UserId, $ContractId], 'ssss');
					} else {
							$query = "INSERT INTO ServiceContracts (
												StatusId,
												ContractName,
												UserId,
												TransDate)

						       VALUES (?, ?, ?, NOW())";
						return $this->execute($query, [$StatusId, $ContractName, $UserId], 'sss');
					}
			}					

			/* Get Service Contract Category Data
             ===============================	*/		
			function getServiceContractCategories($ContractId) {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as ContractCategoryId,
										ContractId,
										a.StatusId,
										ContractName,
										ContractCategoryName,
										COALESCE((Select group_concat( UnitName  
                                         SEPARATOR ', ' ) 
											FROM ClientUnits c
											WHERE a.Id = c.ContractCategoryId ),'') as  ParticipUnits,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractCategories a,
									ServiceContracts c,
									Users b
							WHERE a.UserId = b.UserId	
							AND a.ContractId = c.Id
							AND a.ContractId = ?
							ORDER BY Id";

                        return $this->getAll($query, [$ContractId], 's');
						
			}	

			//  Update Service Contract Info 
            //=======================			
			function setServiceContractCategory(   
										$ContractCategoryId,
										$ContractId,
										$StatusId,
										$ContractCategoryName,
										$UserId ) 
			{
					
					if(is_numeric($ContractCategoryId))  {

                         $query = "UPDATE ServiceContractCategories
							SET StatusId = ?,
								ContractCategoryName = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";
						return $this->execute($query, [$StatusId, $ContractCategoryName, $UserId, $ContractCategoryId], 'ssss');
					} else {
							$query = "INSERT INTO ServiceContractCategories (
												ContractId,
												StatusId,
												ContractCategoryName,
												UserId,
												TransDate)

						       VALUES (?, ?, ?, ?, NOW())";
						return $this->execute($query, [$ContractId, $StatusId, $ContractCategoryName, $UserId], 'ssss');
					}

			}	

			//==================================			
			// Get Service Contract Category Rates
			//==================================			
			function getServiceContractCategoryRates($ContractCategoryId,  $ServiceTypeId, $RateDateRangeId) {
			
 
							
                        $query = "CALL proc_getServiceContractCategoryRates (?, ?, ?)";
                        return $this->getAll($query, [$ContractCategoryId, $ServiceTypeId, $RateDateRangeId], 'sss');
						
			}
			
			
			/*  Update Service Contract Category Rates 
            ========================================*/			
			function setServiceContractCategoryRates(   $ContractCategoryId,
													$ServiceTypeId,
													$RateDateRangeId,
													$ShiftId,
													$RateTypeId,
													$BillRate,
													$PayRate,
													$UserId ) 
			{
					

							$query = "INSERT INTO ServiceContractCategoryRates (
												ContractCategoryId,
												ServiceTypeId,
												RateDateRangeId,
												ShiftId,
												RateTypeId,
												BillRate,
												PayRate,
												UserId,
												TransDate)

						       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
								ON DUPLICATE KEY UPDATE PayRate = VALUES(PayRate), BillRate = VALUES(BillRate)";

				return $this->execute($query, [$ContractCategoryId, $ServiceTypeId, $RateDateRangeId, $ShiftId, $RateTypeId, $BillRate, $PayRate, $UserId], 'ssssssss');
			}	
			
			/* Get Service Contract Category Override Rates
             ===========================================	*/		
			function getServiceContractCategoryOvrRates($ContractCategoryId, $ServiceTypeId, $RateDateRangeId) {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as ContractCategoryRatesOvrId,
										ContractCategoryName,
										ContractCategoryId,
										ServiceTypeDesc,
										ServiceTypeId,
										RateDateRangeId,
										WeekDayId,
										CASE WeekDayId 
											WHEN '1' THEN 'Sun'
											WHEN '2' THEN 'Mon'
											WHEN '3' THEN 'Tue'
											WHEN '4' THEN 'Wed'
											WHEN '5' THEN 'Thu'
											WHEN '6' THEN 'Fri'
											WHEN '7' THEN 'Sat'
										END AS WeekDayName,
										ShiftId,
										CASE ShiftId 
											WHEN '1' THEN 'Day'
											WHEN '2' THEN 'Evening'
											WHEN '3' THEN 'Night'
											WHEN '4' THEN '12 Hrs Day'
											WHEN '5' THEN '12 Hrs Night'
										END AS ShiftName,
										BillPayFL,
										CASE BillPayFL 
											WHEN '1' THEN 'Pay'
											WHEN '2' THEN 'Bill'
										END AS BillPayFLName,
									
										OverrideRate,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractCategoryRatesOvr a,
									Users b,
									ServiceContractCategories c,
									ServiceTypes f
							WHERE a.UserId = b.UserId	
							AND a.ContractCategoryId =  '{$ContractCategoryId}'	
							AND a.ServiceTypeId =  '{$ServiceTypeId}'	
							AND a.RateDateRangeId =  '{$RateDateRangeId}'
							AND a.ServiceTypeId = f.Id
							AND a.ContractCategoryId = c.Id
							ORDER BY WeekDayId";

                        return $this->getAll($query);
						
			}	

			/*  Update Service Contract Category Rates Override
            ================================================= */			
			function setServiceContractCategoryOvrRates(  	$ContractCategoryRatesOvrId,
														$ContractCategoryId,
														$ServiceTypeId,
														$RateDateRangeId,
														$WeekDayId,
														$ShiftId,
														$BillPayFL,
														$OverrideRate,
														$UserId 
													) 
			{
					

					if(is_numeric($ContractCategoryRatesOvrId))  {

                         $query = "UPDATE ServiceContractCategoryRatesOvr
							SET WeekDayId = ?,
								ShiftId = ?,
								BillPayFL = ?,
								OverrideRate = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";
						return $this->execute($query, [$WeekDayId, $ShiftId, $BillPayFL, $OverrideRate, $UserId, $ContractCategoryRatesOvrId], 'ssssss');
					} else {
							$query = "INSERT INTO ServiceContractCategoryRatesOvr (
												ContractCategoryId,
												ServiceTypeId,
												RateDateRangeId,
												WeekDayId,
												ShiftId,
												BillPayFL,
												OverrideRate,
												UserId,
												TransDate)

						       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
						return $this->execute($query, [$ContractCategoryId, $ServiceTypeId, $RateDateRangeId, $WeekDayId, $ShiftId, $BillPayFL, $OverrideRate, $UserId], 'ssssssss');
					}

			}			
			
			/*  Update Service Contract Category Rates Override
            ================================================= */			
			function setDeleteServiceContractCategoryOvrRate($ContractCategoryRatesOvrId)			
                        
			{			$query = "DELETE FROM ServiceContractCategoryRatesOvr
							WHERE Id = ?";

                        return $this->execute($query, [$ContractCategoryRatesOvrId], 's');
						
			}	

			/* Get Clients Un-Assigned to Contracts Data
            //=======================	*/		
			function getServiceContractUnassignedClients() {
			
                        $query = "	SELECT	Id as id,
											ClientName
									FROM Clients
									WHERE ContractId = 0
										ORDER BY ClientName";

                        return $this->getAll($query);
						
			}	

			/* Get Clients Assigned to Contracts Data
            //========================================	*/		
			function getServiceContractAssignedClients($ContractId) {
			
                        $query = "	SELECT	Id as id,
											ClientName
									FROM Clients
									WHERE ContractId = ?
										ORDER BY ClientName";

                        return $this->getAll($query, [$ContractId], 's');
						
			}	

			/*  Update Service Contract Un-Assigned Clients     
             ===================================================*/			
			function setServiceContractUnassignedClients(	$ContractId,	
															$ClientsArray,	
															$UserId) {
					
 
	
					foreach ($ClientsArray as $ClientData) {
								
							$ClientId = $ClientData['id'];
							
												
						$query = "UPDATE Clients
									SET ContractId = 0,
									    UserId = ?,
										TransDate = NOW()

								WHERE Id = ?
								AND ContractId != 0";

						$this->execute($query, [$UserId, $ClientId], 'ss');
						
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/*  Update Service Contract Assigned Clients     
             ===================================================*/			
			function setServiceContractAssignedClients(	$ContractId,	
														$ClientsArray,	
														$UserId) {
					
 
	
					foreach ($ClientsArray as $ClientData) {
								
							$ClientId = $ClientData['id'];
							
							
												
						$query = "UPDATE Clients
									SET ContractId = ?,
									    UserId = ?,
										TransDate = NOW()
								WHERE Id = ?
								AND ContractId != ?";

						$this->execute($query, [$ContractId, $UserId, $ClientId, $ContractId], 'ssss');
						
					} // end of 'foreach' loop

				return true;
			}	

			/* Get Client Units Un-Assigned to Contract Categories Data
            //========================================================	*/		
			function getServiceContractCategoryUnassignedClientUnits($ClientId) {
			
                        $query = "	SELECT	Id as id,
											UnitName
										FROM ClientUnits 
									WHERE 	ClientId = '{$ClientId}' 
										AND	ContractCategoryId = 0
									ORDER BY UnitName";

                        return $this->getAll($query);
						
			}	

			/* Get Client Units Assigned to Contract Categories Data
           //========================================================	*/		
			function getServiceContractCategoryAssignedClientUnits($ClientId, $ContractCategoryId) {
			
                        $query = "	SELECT	Id as id,
											UnitName
									FROM ClientUnits
									WHERE ClientId = '{$ClientId}' 
									AND ContractCategoryId = ?
										ORDER BY UnitName";

                        return $this->getAll($query, [$ContractCategoryId], 's');
						
			}				

			/* Get Service Contract Holidays Data
             ===============================	*/		
			function getServiceContractHolidays($ContractId, $CalendayYear) {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as HolidayId,
										ContractId,
										ContractName,
										HolidayDate as HolidayDateSort,
										DATE_FORMAT(HolidayDate, '%m-%d-%Y' ) as HolidayDate,
										HolidayDesc,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractHolidays a, Users b, ServiceContracts c
							WHERE ContractId =  '{$ContractId}'
							AND CalendarYear = '{$CalendayYear}'	
							AND a.UserId = b.UserId		
							AND a.ContractId = c.Id
							ORDER BY HolidayDateSort";

                        return $this->getAll($query);
						
			}	
			
			
			// Get Clients With Contract Listing
            //=======================			
			function getClientsWithContract() {
                        
						$query = "SELECT 	a.Id as id, 
											a.Id as ClientId, 
											ClientName,
											b.id as ContractId,
											ContractName
						FROM Clients a, ServiceContracts b
						WHERE a.ContractId = b.Id
							ORDER BY ClientName";

                        return $this->getAll($query);
						
			}

			/*  Update Service Contract Un-Assigned Units     
             ===================================================*/			
			function setServiceContractUnassignedUnits(	$ContractCategoryId,	
														$UnitsArray,	
														$UserId) {
					
 
	
					foreach ($UnitsArray as $UnitData) {
								
							$ClientUnitId = $UnitData['id'];
							
												
						$query = "UPDATE ClientUnits
									SET  	ContractCategoryId = 0,
									    UserId = ?,
										TransDate = NOW()

								WHERE Id = ?
								AND ContractCategoryId != 0";

						$this->execute($query, [$UserId, $ClientUnitId], 'ss');
						
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/*  Update Service Contract Assigned Units     
             ===================================================*/			
			function setServiceContractAssignedUnits(	$ContractCategoryId,	
														$UnitsArray,	
														$UserId) {
					
 
	
					foreach ($UnitsArray as $UnitData) {
								
							$ClientUnitId = $UnitData['id'];
							
							
												
						$query = "UPDATE ClientUnits
									SET ContractCategoryId = ?,
									    UserId = ?,
										TransDate = NOW()
								WHERE Id = ?";


						$this->execute($query, [$ContractCategoryId, $UserId, $ClientUnitId], 'sss');
						
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			/* Get All Service Contracts with Categories Data
             ===============================	*/		
			function getServiceContractsWithCategories() {
			
                        $query ="SELECT a.Id as id,
                                        a.Id as ContractCategoryId,
										ContractId,
										a.StatusId,
										ContractName,
										ContractCategoryName,
										COALESCE((Select group_concat( UnitName  
                                         SEPARATOR ', ' ) 
											FROM ClientUnits c
											WHERE a.Id = c.ContractCategoryId ),'') as  ParticipUnits,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	ServiceContractCategories a,
									ServiceContracts c,
									Users b
							WHERE c.StatusId = '1' 
							AND a.UserId = b.UserId	
							AND a.ContractId = c.Id
							ORDER BY ContractName,  ContractCategoryName";

                        return $this->getAll($query);
						
			}

			/* Get Rate Date Ranges Data
             ===============================	*/		
			function getRatesDateRanges() {
			
                        $query ="SELECT a.Id AS id, 
										a.Id AS RateDateRangeId, 
										DATE_FORMAT( StartDate, '%m-%d-%Y') AS StartDateDesc,
										StartDate,
										EndDate,
    									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										
							FROM	ServiceContractCategoryRateDateRanges a, Users b  
							WHERE a.UserId = b.UserId
							ORDER BY StartDate";

                        return $this->getAll($query);
						
			}	
			/* Get Client Invoices Header Data
             ===================================	*/		
			function getClientInvoicesHeader($ClientId) {
			
                        $query ="SELECT Id as id,
										Id as InvoiceNumber,
										ClientId, 
										SearchId, 
										StatusId, 
										InvoiceDate, 
										DATE_FORMAT(InvoiceDate, '%m-%d-%Y' ) as InvoiceDate,
										PayrollWeek, 
										TotalUnits, 
										TotalAmount, 
										PaidAmount,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
										FROM InvoiceHeader a,Users b  
									WHERE ClientId = ?
									AND a.UserId = b.UserId";

                        return $this->getAll($query, [$ClientId], 's');
						
			}			

			/* Get Client Invoice Details Data
             ===================================	*/		
			function getClientInvoiceDetails($InvoiceNumber) {
			
                        $query ="SELECT a.InvoiceNumber,
										LineNumber,
										SequenceNumber,
										CurrentLineFL,
										a.ScheduleId,
										DATE_FORMAT( c.ServiceDate, '%m/%d/%Y' ) as ServiceDateString,
										CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as RegistrantName,
										c.ClientUnitId,
										UnitName,
										c.ServiceTypeId,
										ServiceTypeDesc,
										ShiftName,
										ShiftType,
										a.Units,
										a.BillRate,
										a.PayRate,
										ROUND((a.BillRate * a.Units),2)  as BillAmount,
										ROUND((a.PayRate * a.Units),2)  as PayAmount,
										CONCAT( trim( g.FirstName ) , ' ', trim( g.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	InvoiceDetails a, 
										Registrants b, 
										WeeklyServices c, 
										ClientUnits d, 
										ServiceTypes f,
										Shifts e,
										Users g
								WHERE a.InvoiceNumber = '{$InvoiceNumber}'
								AND a.ScheduleId = c.Id	
								AND c.RegistrantId = b.Id
								AND c.ClientUnitId = d.Id
								AND c.ServiceTypeId = f.Id
								AND c.ShiftId = e.ShiftId
								AND a.UserId = g.UserId
								ORDER BY LineNumber, SequenceNumber DESC, c.ShiftId";

                        return $this->getAll($query);
						

			}			

			/* Set Client Invoice Detail Adjusted Data
             =========================================	*/		
			function setAdjustClientInvoiceLineItem(	$InvoiceNumber,
														$LineNumber,
														$Units,
														$UnitsOrig,
														$BillRate,
														$BillAmount,
														$BillAmountOrig,
														$ScheduleId,
														$ClientUnitId,
														$ServiceTypeId,
														$UserId 
													) 
			{
			
                        $query = "UPDATE InvoiceDetails a, WeeklyServices b, InvoiceHeader c
										SET a.Units = ?,
											a.BillRate = ?,
											a.Amount = ?,
											a.UserId = ?,
											a.TransDate = NOW(),
											b.ClientUnitId = ?,
											b.ServiceTypeId = ?,
											b.UserId = ?,
											b.TransDate = NOW(),
											c.TotalUnits = c.TotalUnits - ? + ?,
											c.TotalAmount = ROUND((c.TotalAmount - ? + ?), 2),
											c.UserId = ?,
											c.TransDate = NOW()


								WHERE 	a.InvoiceNumber = ?
								AND     a.LineNumber = ?
								AND     b.Id = ?
								AND     c.Id = ?";


                       return $this->execute($query, [$Units, $BillRate, $BillAmount, $UserId, $ClientUnitId, $ServiceTypeId, $UserId, $UnitsOrig, $Units, $BillAmountOrig, $BillAmount, $UserId, $InvoiceNumber, $LineNumber, $ScheduleId, $InvoiceNumber], 'ssssssssssssssss');
						
			}			

			/* Get Client Invoice Payments Data
             ===================================	*/		
			function getClientInvoicePayments($InvoiceNumber) {
			
                        $query ="SELECT Id as PaymentId,
										PaymentDate,
										DATE_FORMAT(PaymentDate, '%m-%d-%Y' ) as PaymentDateString,
										ReferenceNumber,
										PaymentAmount,
										PaymentAmount as PaymentAmountOrig,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	InvoicePayments a, 
										Users b
								WHERE InvoiceNumber = '{$InvoiceNumber}'
								AND a.UserId = b.UserId
								ORDER BY Id DESC";

                        return $this->getAll($query);
						
			}		

			/* Set Client Invoice Payments Data
             ===================================	*/		
			function setClientInvoicePayment(	$PaymentId,
												$InvoiceNumber,
												$PaymentAmount,
												$ReferenceNumber,
												$UserId
												) 
			{
			
					if(is_numeric($PaymentId))  {

                         $query = "UPDATE InvoicePayments
							SET PaymentAmount = ?,
								ReferenceNumber = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";
						return $this->execute($query, [$PaymentAmount, $ReferenceNumber, $UserId, $PaymentId], 'ssss');
					} else {
							$query = "INSERT INTO InvoicePayments (
												InvoiceNumber,
												PaymentDate,
												PaymentAmount,
												ReferenceNumber,
												UserId,
												TransDate)

						       VALUES (?, CURDATE(), ?, ?, ?, NOW())";
						return $this->execute($query, [$InvoiceNumber, $PaymentAmount, $ReferenceNumber, $UserId], 'ssss');
					}
						
			}		
			 
			// Set Client Invoice Header Payments 
            //===================================			
			function setClientInvoicePaidAmount( 	$InvoiceNumber,
													$PaymentAmount,
													$PaymentAmountOrig,
													$UserId) 
			{ 
                        $query = "UPDATE InvoiceHeader
							SET PaidAmount = PaidAmount - ? + ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

						return $this->execute($query, [$PaymentAmountOrig, $PaymentAmount, $UserId, $InvoiceNumber], 'ssss');
			}
			 
			/* Get Client Invoice Adjustments Data
             ===================================	*/		
			function getClientInvoiceAdjustments($InvoiceNumber) {
			
                        $query ="SELECT a.Id as id, 
										a.id as AdjustmentId,
										a.InvoiceNumber,
										AdjAmount,
										AdjReason,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	InvoiceAdjustments a, 
										Users b
								WHERE a.InvoiceNumber = '{$InvoiceNumber}'
								AND a.UserId = b.UserId
								ORDER BY a.Id";

                        return $this->getAll($query);
						
			}		

			/* Set Client Invoice Adjustment Data
             ===================================	*/		
			function setClientInvoiceAdjustment(	$AdjustmentId,
													$InvoiceNumber,
													$AdjAmount,
													$AdjReason,
													$UserId
												) 
			{
			
					if(is_numeric($AdjustmentId))  {

                         $query = "UPDATE InvoiceAdjustments
							SET AdjAmount = ?,
								AdjReason = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";
						return $this->execute($query, [$AdjAmount, $AdjReason, $UserId, $AdjustmentId], 'ssss');
					} else {
							$query = "INSERT INTO InvoiceAdjustments (
												InvoiceNumber,
												AdjAmount,
												AdjReason,
												UserId,
												TransDate)

						       VALUES (?, ?, ?, ?, NOW())";
						return $this->execute($query, [$InvoiceNumber, $AdjAmount, $AdjReason, $UserId], 'ssss');
					}
						
			}	

			// Set Client Invoice Header Adjusted Total Amount 
            //====================================================			
			function deleteClientInvoiceAdjustment( $AdjustmentId )
			{ 
                        $query = "DELETE FROM InvoiceAdjustments
									WHERE Id = ?";

						return $this->execute($query, [$AdjustmentId], 's');
			}

			// Set Client Invoice Header Adjusted Total Amount 
            //====================================================			
			function setClientInvoiceTotalAmount( 	$InvoiceNumber,
													$AdjAmount,
													$AdjAmountOrig,
													$UserId) 
			{ 
                        $query = "UPDATE InvoiceHeader
                                   SET TotalAmount = TotalAmount - ? + ?,
									UserId = ?,
                                    TransDate = NOW()
								WHERE Id = ?";

						return $this->execute($query, [$AdjAmountOrig, $AdjAmount, $UserId, $InvoiceNumber], 'ssss');
			}
			
			// Set Generate New Client's Invoices
            //==========================================			
			function setGenerateNewInvoicesData(	$ClientId, 
													$PerStartDateMon,		
													$PerStartDateTue,
													$PerEndDateSun,
													$UserId)
	{
			
  	$query = "CALL proc_GenerateClientNewInvoicesData(?, ?, ?, ?, ?)";

                        return $this->getAll($query, [$ClientId, $PerStartDateMon, $PerStartDateTue, $PerEndDateSun, $UserId], 'sssss');
			}					

			/* Check for Duplicate Holiday Date
            //=====================================*/					
			function getDuplicateHolidays($ContractId, $CalendayYear, $HolidayDate) {
                         

                        $query = "SELECT *
									FROM ServiceContractHolidays
										WHERE ContractId = ?
										AND CalendarYear = ?
										AND HolidayDate = ?";

						return $this->getAll($query, [$ContractId, $CalendayYear, $HolidayDate], 'sss');
						//return $query;
	                                 
            }  	
			// Set Service Contract Holiday Data  
            //====================================================			
			function data_setServiceContractHoliday($ContractId,
													$CalendarYear,
													$HolidayDate,
													$HolidayDesc,
													$UserId) 
			{ 
                        $query = "INSERT INTO ServiceContractHolidays (
												ContractId,
												CalendarYear,
												HolidayDate,
												HolidayDesc,
												UserId,
												TransDate)

						       VALUES (?, ?, ?, ?, ?, NOW())";

						return $this->execute($query, [$ContractId, $CalendarYear, $HolidayDate, $HolidayDesc, $UserId], 'sssss');
			}

			/* Get Payroll Side Navigation Data
            //=======================	*/		
			function getPayrollSideNavigation() {
			
                        $query = "SELECT *							
							FROM PayrollSideNavigation
						ORDER BY Id";

                        return $this->getAll($query);
						
			}	

			// Get Client Services History Charts Data 
            //=============================================			
		 	function getClientServHistChartData($ClientId) {
			
                        $query ="SELECT distinct PayrollWeek as PayrollWeekSort,
                                                 DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) as PayrollWeek, 

						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId = 0) as 'Pending Shifts',           

						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId in (5, 6)) as 'Un-Confirmed Shifts',           

						( Select count(*) from WeeklyServices c 
							where a.ClientId = c.ClientId   
							AND a.PayrollWeek = c.PayrollWeek
							AND c.ScheduleStatusId in (2,3,4)) as 'Cancelled Shifts',           

						( Select count(*) from WeeklyServices d 
							where a.ClientId = d.ClientId   
							AND a.PayrollWeek = d.PayrollWeek
							AND d.ScheduleStatusId > 6) as 'Confirmed Shifts',           

						( Select count(*) from WeeklyServices e 
							where a.ClientId = e.ClientId   
							AND a.PayrollWeek = e.PayrollWeek
							AND e.ScheduleStatusId = 1) as 'Un-Used Availability'           
							FROM WeeklyServices a
							WHERE ClientId = '{$ClientId}'
							AND PayrollWeek > (ADDDATE(CURDATE(), -120))
							ORDER BY PayrollWeekSort";

                        return $this->getAll($query);
			}			

			// Get Client Services History Charts Data 
            //=============================================			
		 	function getAllServHistChartData() {
			
                        $query ="SELECT distinct PayrollWeek as PayrollWeekSort,
                                                 DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) as PayrollWeek, 
						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId = 0) as 'Pending Shifts',           
												 
						( Select count(*) from WeeklyServices b 
							where a.ClientId = b.ClientId   
							AND a.PayrollWeek = b.PayrollWeek
							AND b.ScheduleStatusId in (5, 6)) as 'Un-Confirmed Shifts',           

						( Select count(*) from WeeklyServices c 
							where a.ClientId = c.ClientId   
							AND a.PayrollWeek = c.PayrollWeek
							AND c.ScheduleStatusId in (2,3,4)) as 'Cancelled Shifts',           

						( Select count(*) from WeeklyServices d 
							where a.ClientId = d.ClientId   
							AND a.PayrollWeek = d.PayrollWeek
							AND d.ScheduleStatusId > 6) as 'Confirmed Shifts',           

						( Select count(*) from WeeklyServices e 
							where a.ClientId = e.ClientId   
							AND a.PayrollWeek = e.PayrollWeek
							AND e.ScheduleStatusId = 1) as 'Un-Used Availability'           
							FROM WeeklyServices a
							WHERE PayrollWeek > (ADDDATE(CURDATE(), -30))
							ORDER BY PayrollWeekSort";

                        return $this->getAll($query);
			}			

			/* Get Client Work Orders Data
             ===================================	*/		
			function getClientWorkOrders($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.id as WorkOrderId,
										a.ClientId,
										a.StatusId,
										a.OrderTypeId,
										OrderTypeDesc,
										OrderDate as OrderDateSort,
										DATE_FORMAT(OrderDate, '%m-%d-%Y' ) as OrderDate,
										DATE_FORMAT(StartDate, '%m-%d-%Y' ) as StartDate,
										DATE_FORMAT(EndDate, '%m-%d-%Y' ) as EndDate,
										a.Comments,
										(SELECT COUNT(*) FROM WeeklyServices d
											WHERE d.WorkOrderId = a.Id
											AND ScheduleStatusId > 6) as ShiftsCount,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	ClientWorkOrders a, 
										ClientOrderTypes b,
										Users c
								WHERE a.ClientId = '{$ClientId}'
								AND a.UserId = c.UserId
								AND a.OrderTypeId = b.Id
								ORDER BY OrderDateSort DESC";

                        return $this->getAll($query);
						
			}		

			//======================================================
			function setWorkOrder(	$WorkOrderId,
									$ClientId,
									$OrderTypeId,
									$StartDate,
									$EndDate,
									$Comments,	
									$UserId) {
					
					if(is_numeric($WorkOrderId))  {

                         $query = "UPDATE ClientWorkOrders
							SET OrderTypeId = ?,
								StartDate = ?,
								EndDate = ?,
								Comments = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";
						return $this->execute($query, [$OrderTypeId, $StartDate, $EndDate, $Comments, $UserId, $WorkOrderId], 'ssssss');
					} else {
							$query = "INSERT INTO ClientWorkOrders (
											  ClientId,
											  OrderDate,
											  OrderTypeId,
											  StartDate,
											  EndDate,
											  Comments,
											  UserId,
											  TransDate)

						       VALUES (?, CURDATE(), ?, ?, ?, ?, ?, NOW())";
						return $this->execute($query, [$ClientId, $OrderTypeId, $StartDate, $EndDate, $Comments, $UserId], 'ssssss');
					}
			}				

			/* Get Client Active Work Orders Data
             ===================================	*/		
			function getClientActiveWorkOrders($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.id as WorkOrderId,
										a.OrderTypeId,
										OrderTypeDesc,
										OrderDate as OrderDateSort,
										DATE_FORMAT(OrderDate, '%m-%d-%Y' ) as OrderDate,
										DATE_FORMAT(StartDate, '%m-%d-%Y' ) as StartDate,
										DATE_FORMAT(EndDate, '%m-%d-%Y' ) as EndDate,
										a.Comments,
										(SELECT COUNT(*) FROM WeeklyServices d
											WHERE d.WorkOrderId = a.Id
											AND ScheduleStatusId > 6) as ShiftsCount,
										CONCAT( trim( c.FirstName ) , ' ', trim( c.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
								FROM 	ClientWorkOrders a, 
										ClientOrderTypes b,
										Users c
								WHERE a.ClientId = '{$ClientId}'
								AND a.StatusID = '1'
								AND a.UserId = c.UserId
								AND a.OrderTypeId = b.Id
								ORDER BY OrderDateSort DESC";

                        return $this->getAll($query);
						
			}	
			/* Get Client Pending Daily Shifts
             ===================================	*/		
			function getClientDailyPendingShifts ($ClientId, $ServiceDate, $ShiftId) {
			
                        $query ="SELECT a.Id AS ScheduleId, 
										ScheduleStatusId,
										ServiceTypeId,
										ServiceTypeDesc, 
										ClientUnitId, 
										COALESCE((SELECT UnitName FROM ClientUnits c
											WHERE a.ClientUnitId = c.Id), '') as UnitName,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										LunchHour,
										TotalHours,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
								FROM 	WeeklyServices a, 
										Users e,
										ServiceTypes b								
									Where 	a.ClientId= '{$ClientId}' 
									AND  ServiceDate =  '{$ServiceDate}'
									AND a.ShiftId =  '{$ShiftId}'
									AND ScheduleStatusId =  '0'
									AND a.UserId = e.UserId
									AND a.ServiceTypeId = b.Id";

                        return $this->getAll($query);
						
			}	

			/* Get Registrants for Pending Shifts
             ===================================	*/		
			function getRegistrantsForPendingShifts (	$ClientId, 
														$ClientUnitId,
														$ServiceTypeId,
														$ServiceDate, 
														$PayrollWeek, 
														$ShiftId) 
			{
				
				if ($ServiceTypeId == '1' ) {

                    				$query ="SELECT
									
									COALESCE((SELECT 'Yes' FROM RegistrantVerifiedAvailability d
										WHERE a.RegistrantId = d.RegistrantId 
										AND d.ServiceDate = '{$ServiceDate}'
										AND d.ShiftId = '{$ShiftId}'),'') as AvailVerfFL, 
									COALESCE((Select group_concat( CredItemDesc
										SEPARATOR ', ' ) 
										FROM RegistrantCredItems k,  CredentialingItems h
										WHERE a.RegistrantId = k.RegistrantId
										  /*AND  k.StatusId in (1,2)*/ 	
                                          AND  k.CredItemId = h.Id ),'')  as CredItems,	
									COALESCE((SELECT  CASE count(*) 
										WHEN 0 THEN ''
											ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
										END  
									FROM RegistrantAttchedSpecialties k, Specialties h
										where 	h.Id = k.SpecialtyId
												and  a.RegistrantId = k.RegistrantId),'') as SpecialitiesList,  										  
									COALESCE((SELECT i.Comments FROM RegistrantAvailabilityComments i
										WHERE a.RegistrantId = i.RegistrantId 
										AND i.ServiceDate = '{$ServiceDate}'
										AND i.ShiftId = '{$ShiftId}'),'') as Comments, 
									COALESCE((SELECT sum(TotalHours) 
										 from WeeklyServices h
										Where a.RegistrantId = h.RegistrantId	
										AND h.PayrollWeek = '{$PayrollWeek}' 
										AND h.ScheduleStatusId > 4),'') as HoursScheduled,										
									a.RegistrantId,
									f.RegistrantGroupId,
									COALESCE(Availability, '') as Availability,
									COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,
									CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
									DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrantsNonRest a, 
							Users b,
							Registrants c,
							RegistrantTypes f
						
					WHERE a.UserId = b.UserId
					AND a.ClientId = '{$ClientId}' 
					AND a.ClientUnitId = '{$ClientUnitId}'
					AND a.ServiceTypeId = '{$ServiceTypeId}'
					AND a.RegistrantId = c.Id
					AND c.TypeId = f.Id
					AND c.StatusId = '1'
					AND a.Status = '1' 
					AND NOT EXISTS (SELECT 1 FROM WeeklyServices g
									  WHERE a.RegistrantId = g.RegistrantId
									  AND g.ServiceDate = '{$ServiceDate}'
									  AND g.ShiftId = '{$ShiftId}')			
				Order By c.LastName, c.FirstName ";

				} else {

                    				$query ="SELECT
									
									COALESCE((SELECT 'Yes' FROM RegistrantVerifiedAvailability d
										WHERE a.RegistrantId = d.RegistrantId 
										AND d.ServiceDate = '{$ServiceDate}'
										AND d.ShiftId = '{$ShiftId}'),'') as AvailVerfFL, 
									COALESCE((Select group_concat( CredItemDesc
										SEPARATOR ', ' ) 
										FROM RegistrantCredItems k,  CredentialingItems h
										WHERE a.RegistrantId = k.RegistrantId
										  /*AND  k.StatusId in (1,2)*/ 	
                                          AND  k.CredItemId = h.Id ),'')  as CredItems,	
									COALESCE((SELECT  CASE count(*) 
										WHEN 0 THEN ''
											ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
										END  
									FROM RegistrantAttchedSpecialties k, Specialties h
										where 	h.Id = k.SpecialtyId
												and  a.RegistrantId = k.RegistrantId),'') as SpecialitiesList,  										  
									COALESCE((SELECT i.Comments FROM RegistrantAvailabilityComments i
										WHERE a.RegistrantId = i.RegistrantId 
										AND i.ServiceDate = '{$ServiceDate}'
										AND i.ShiftId = '{$ShiftId}'),'') as Comments, 
									COALESCE((SELECT sum(TotalHours) 
										 from WeeklyServices h
										Where a.RegistrantId = h.RegistrantId	
										AND h.PayrollWeek = '{$PayrollWeek}' 
										AND h.ScheduleStatusId > 4),'') as HoursScheduled,										
									a.RegistrantId,
									f.RegistrantGroupId,
									COALESCE(Availability, '') as Availability,
									COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,
									CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
									CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
									DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrantsNonRest a, 
							Users b,
							Registrants c,
							RegistrantTypes f
						
					WHERE a.UserId = b.UserId
					AND a.ClientId = ?
					AND a.ServiceTypeId = ?
					AND a.RegistrantId = c.Id
					AND c.TypeId = f.Id
					AND a.Status = '1'
					AND c.StatusId = '1'
					AND NOT EXISTS (SELECT 1 FROM WeeklyServices g
									  WHERE a.RegistrantId = g.RegistrantId
									  AND g.ServiceDate = ?
									  AND g.ShiftId = ?)
				ORDER BY c.LastName, c.FirstName";

				}

                        return $this->getAll($query, [$ClientId, $ServiceTypeId, $ServiceDate, $ShiftId], 'ssss');
						
			}	

			/* Set Add Client Pending Shifts Data
             ===================================	*/		
			function setClientAddPendingShifts(	$ClientId, 
												$ClientUnitId, 
												$ScheduleStatusId,
												$ServiceDate,
												$WeekDay, 
												$PayrollWeek,
												$ShiftId,
												$ServiceTypeId,
												$UserId
											) 
			{
			
					 
					 $query = "INSERT INTO WeeklyServices (
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,
												StartTime,
												EndTime,
												LunchHour,
												TotalHours,
												WeekDay,
												PayrollWeek,
												ShiftId,
												ServiceTypeId,
												UserId,
												TransDate)

								SELECT	?,
										?,
										?,
										?,
										StartTime,
                                        EndTime,
                                        LunchHour,
										TotalHours,
										?,
										?,
                                        ?,
										?,
 										?,
                                         NOW()
								FROM ClientShifts
								WHERE ClientId = ?
								 AND ShiftId = ?";

						return $this->execute($query, [$ClientId, $ClientUnitId, $ScheduleStatusId, $ServiceDate, $WeekDay, $PayrollWeek, $ShiftId, $ServiceTypeId, $UserId, $ClientId, $ShiftId], 'sssssssssss');
						
			}			

			/* Set Add Client Pending Shifts Data
             ===================================	*/		
			function setRegistrantAvailabilityComments(	$RegistrantId, 
														$ServiceDate,
														$ShiftId,
														$Comments,
														$UserId
													) 
			{
			
					 

                         $query = "REPLACE INTO RegistrantAvailabilityComments (
									RegistrantId,
									ServiceDate,
									ShiftId,
									Comments,
									UserId,
									TransDate)

									VALUES (?, ?, ?, ?, ?, NOW())";

                       return $this->execute($query, [$RegistrantId, $ServiceDate, $ShiftId, $Comments, $UserId], 'sssss');
						
			}	
			
			/* Set Add Client Pending Shifts Data
             ===================================	*/		
			function setMatchPendShiftsRegistrants(	$Data,
													$UserId) {
					
							
 
					// Match Pedning Shifts with Registrants   
					//===============================================					

					foreach ($Data as $rec) {
								
							$ScheduleId = $rec['ScheduleId'];
							$RegistrantId = $rec['RegistrantId'];

							$query = "UPDATE WeeklyServices
									SET ScheduleStatusId = '6',
										RegistrantConfFL = '1',
										RegistrantId = ?,
										UserId = ?,
										TransDate = NOW()
									WHERE Id = ?";

							$this->execute($query, [$RegistrantId, $UserId, $ScheduleId], 'sss');
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}	

			/* Get Registrant View "Attention" Credentialing Items 
             ===================================	*/		
			function getRegistrantViewAttnCredItems ($RegistrantId) {
			
                        $query ="Select RegistrantId,
										a.CredItemId,
										a.StatusId,
										b.CredItemCategory,
										CredItemType,
										CredItemDesc,
										a.CredItemStatus,
										ComplianceLevelId,
 										CASE (SELECT 1 FROM RegistrantDocuments  h
											WHERE a.RegistrantId = h.RegistrantId
											AND   a.CredItemId = h.DocumentTypeId
											AND   AwatingVerification = '1') 
											WHEN '1' THEN 'Document Uploaded. Awating Verifications'
											ELSE 'Click to Upload New Document'
										END AS DocumentVerified,

										COALESCE((SELECT 'uploaded' FROM RegistrantDocuments  h
											WHERE a.RegistrantId = h.RegistrantId
											AND   a.CredItemId = h.DocumentTypeId
											AND AwatingVerification = '1'),'expired') as status

									FROM 	RegistrantCredItems a,
											CredentialingItems b
									WHERE a.CredItemId = b.Id 	
									AND RegistrantId = ?
									AND ((ComplianceLevelId = '1') OR (DATEDIFF(ExpirationDate, CURDATE()) BETWEEN 0 AND 30))";

                        return $this->getAll($query, [$RegistrantId], 's');
						
			}

			/* Get Newly Uploaded Credentialing Item Document Awaiting Verification   
             ======================================================================	*/		
			function getRegistrantsCredItemDocsNewlyUploaded () {
			
                        $query ="SELECT a.Id as RegistrantCredItemTransId,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										c.SearchId,
										CredItemDesc,
										a.CredItemId,
										CASE CredItemType
											WHEN '1' THEN 'Needed Once'
											WHEN '2' THEN 'Needs Renewal'
											WHEN '3' THEN 'Conditional (Needs Renewal)'
										END AS CredItemTypeDesc,
										a.CredItemId,
										a.CredItemStatus,
										CredItemStatusDesc,
										CredItemStatusColor,
										CredItemStatusBGColor,
										ComplianceLevelId,
										CredItemType,
										StoredName as StoredDocName,
										COALESCE(a.Comments,'') as Comments,
										COALESCE(DATE_FORMAT( a.ExpirationDate, '%m-%d-%Y' ),'')  as ExpirationDate,
										COALESCE(DATE_FORMAT( a.PrevExpirationDate, '%m-%d-%Y' ),'')  as PrevExpirationDate,
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName, 
										a.TransDate 
							FROM 	RegistrantCredItems a,
									CredentialingItems b,
									Registrants c,
									RegistrantDocuments d,
									RegistrantTypes f,
									CredentialingItemStatuses g,
									Users e 
							WHERE 	a.CredItemId = b.Id 
								AND a.RegistrantId = c.Id
								AND c.TypeId = f.Id
								AND a.CredItemStatus = g.CredItemStatus
								AND a.UserId = e.UserId
								AND a.RegistrantId = d.RegistrantId
								AND a.CredItemId = d.DocumentTypeId
								AND AwatingVerification = '1'
								AND a.CredItemStatus IN (1,3)
							ORDER BY RegistrantName, CredItemDesc";

                        return $this->getAll($query);
						
			}			

			// Set Registrant Credentialing Item Docuemnt Verification Status 
            //====================================================			
			function setRegistrantDocumentVerificationStatus( 	$RegistrantId,
																$CredItemId,
																$AwatingVerification,
																$UserId) 
			{ 
                        $query = "UPDATE RegistrantDocuments
                                   SET AwatingVerification = ?,
									UserId = ?,
                                    TransDate = NOW()
								WHERE RegistrantId = ?
								AND DocumentTypeId = ?";

						return $this->execute($query, [$AwatingVerification, $UserId, $RegistrantId, $CredItemId], 'ssss');
			}

			/* Get Registrant (Blank) Forms   
             ======================================================================	*/		
			function getRegistrantForms () {
			
                        $query = "SELECT FormDesc, StoredDocName FROM RegistrantForms";

                        return $this->getAll($query);
						
			}	

			// Get Cred. Items Not Selected for User Group Listing   
            //=======================			
			function getCredItemsNotSelectedRegGroup ($RegistrantGroupId) {
			
                        $query ="SELECT distinct 	a.Id AS id, 
													CredItemDesc
									FROM CredentialingItems a
								WHERE ConitionalItemFL = '0' 
								AND NOT EXISTS (SELECT 1 FROM RegistrantGroupCredItems c
									WHERE a.Id = c.CredItemId 
									AND   RegistrantGroupId = '{$RegistrantGroupId}')
									ORDER BY CredItemDesc";

                        return $this->getAll($query);
						
			}

			// Get Cred. Items Selected for User Group Listing   
            //=======================			
			function getCredItemsSelectedRegGroup ($RegistrantGroupId) {
			
                        $query ="SELECT distinct 	a.Id AS id, 
													CredItemDesc
									FROM CredentialingItems a
								WHERE ConitionalItemFL = '0' 
								AND  EXISTS (SELECT 1 FROM RegistrantGroupCredItems c
									WHERE a.Id = c.CredItemId 
									AND   RegistrantGroupId = '{$RegistrantGroupId}')
									ORDER BY CredItemDesc";

                        return $this->getAll($query);
			}

			// Get Registrant Groups Listing
            //=======================			
			function getRegistrantGroups() {
			
                        $query = "SELECT Id as id, 	
										 Id as RegistrantGroupId,
									     RegistrantGroupDesc 										 
									FROM RegistrantGroups
									ORDER BY Id";

						return $this->getAll($query);
			}			
			
			//  Update Registrant Group's Credentialing Items     
            //=======================			
			function setRegistrantGroupCredItems($RegistrantGroupId,
												$CredItems,			
												$UserId) 
				{
					
 
							
 
                    // Delete All Existing Cred. Items for selected RegistrantGroupId
					//===============================================					
					$query = "DELETE FROM RegistrantGroupCredItems
								WHERE RegistrantGroupId = ?";

					$this->execute($query, [$RegistrantGroupId], 's');

					// Insert newaly Selected Cred. items for selected RegistrantGroupId
					//========================================================================					

					foreach ($CredItems as $CredItem) {
							
							$CredItemId = $CredItem['id'];			
												
						$query = "INSERT INTO RegistrantGroupCredItems (
						   RegistrantGroupId, CredItemId, UserId, TransDate)
						VALUES (?, ?, ?, NOW())";

						$this->execute($query, [$RegistrantGroupId, $CredItemId, $UserId], 'sss');
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			// Cleitn Orientation Potential Candidates List
			//================================================			
			function getClientOrientationPotentialCandidates($ClientId, $ClientUnitId, $ServiceTypeId) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										a.ClientUnitId,
										a.ServiceTypeId,
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END  
                                            FROM RegistrantAttchedSpecialties c, Specialties b
                                            where b.Id = c.SpecialtyId
                                              and  a.RegistrantId = c.RegistrantId) as SpecialtiesList,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrants a, 
							Users b,
							Registrants c,
							RegistrantTypes f
							
						WHERE a.UserId = b.UserId
						AND a.ClientId = '{$ClientId} '
						AND a.ClientUnitId = '{$ClientUnitId}' 
						AND a.ServiceTypeId = '{$ServiceTypeId}'
						
						AND a.RegistrantId = c.Id
						AND c.TypeId = f.Id
						AND a.Status = 0
						AND a.OrientationId = 0 
					ORDER BY c.LastName, c.FirstName";

                        return $this->getAll($query);
			}	

			// Cleitn Orientation Matched Candidates List
			//==============================================================			
			function getClientOrientationMatchedCandidates($OrientationId) {
			
                        $query ="SELECT a.Id AS id, 
										a.Id as ApprovalId,
										a.SearchId,		
										a.ClientId,
										a.Status,
										CASE a.Status 
											WHEN '0' THEN 'In-Process'
											WHEN '1' THEN 'Approved'
											WHEN '2' THEN 'Dis-Approved'
											WHEN '3' THEN 'ORT Scheduled'
										END AS StatusDesc,
										a.RegistrantId,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
										(SELECT  CASE count(*) 
                                                    WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
											END  
                                            FROM RegistrantAttchedSpecialties c, Specialties b
                                            where b.Id = c.SpecialtyId
                                              and  a.RegistrantId = c.RegistrantId) as SpecialtiesList,
										a.Comments, 
										a.OrientationId,
										COALESCE((SELECT DATE_FORMAT( StartDate, '%m-%d-%Y' )  from ClientOrientations g
																where a.OrientationId = g.Id),'') as OrientationDate,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
					FROM 	ClientApprovedRegistrants a, 
							Users b,
							Registrants c,
							RegistrantAttchedSpecialties d,
							RegistrantTypes f
							
						WHERE a.UserId = b.UserId
						AND a.OrientationId = '{$OrientationId}' 
						AND a.RegistrantId = d.RegistrantId
						AND a.RegistrantId = c.Id
						AND c.TypeId = f.Id
						AND a.Status = 3
					ORDER BY c.LastName, c.FirstName";

                        return $this->getAll($query);
			}		

			/* Set Client Orienttaion Matched Candidates Data
             ===================================	*/		
			function setClientOrientMatchedCandidates(	$OrientationId,
														$Data,
														$UserId) {

					foreach ($Data as $rec) {
								
							$ApprovalId = $rec['ApprovalId'];
							$RegistrantId = $rec['RegistrantId'];

							$query = "UPDATE ClientApprovedRegistrants
									SET Status = '3',
										OrientationId = ?,
										UserId = ?,
										TransDate = NOW()
									WHERE Id = ?";

							$this->execute($query, [$OrientationId, $UserId, $ApprovalId], 'sss');
							
							$query1 = "CALL proc_setClientOrientationCandidateSchedule(?, ?, ?)";
							$this->execute($query1, [$OrientationId, $RegistrantId, $UserId], 'sss');
							
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}	

			// Delete Registrant Credentialing Item Docuemnt Verification Status 
            //====================================================			
			function setDeleteCandidateOrientSchedules( $RegistrantId,
														$OrientationId) 
			{ 
                        $query = "DELETE FROM WeeklyServices
									WHERE RegistrantId = ?
									AND OrientationId = ?";

						return $this->execute($query, [$RegistrantId, $OrientationId], 'ss');
			}
			
			// Get Upcoming Orientations All Info
            //=====================================			
			function getUpcomingOrientationsAll() {
			
                        $query ="SELECT a.Id AS id, 
						                a.Id as OrientationId,     
										a.ClientId,
										ClientName,
										f.SearchId,
										a.ClientUnitId,
										UnitName,
										a.ServiceTypeId,
										ServiceTypeDesc,
										RequestedQty,
										(SELECT count(* ) FROM ClientApprovedRegistrants e
											WHERE a.Id = e.OrientationId) as MatchedQty,
										
										DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate, 
										(SELECT  CASE count(*) 
											WHEN 0 THEN ''
											ELSE group_concat( DATE_FORMAT( OrientAddtnlDate, '%m-%d-%Y' ) 	 SEPARATOR ', ' )
											END 
												FROM ClientOrientationAddtnlDates f
												WHERE a.Id = f.OrientationId) as OrientAddtnlDatesList,
							
										COALESCE(a.Comments, '') as Comments, 
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
									FROM 	ClientOrientations a, 
											Users b, 
											ClientUnits e,
											ServiceTypes h,
											Clients f		
									WHERE a.UserId = b.UserId
									AND a.ClientUnitId = e.Id
									AND a.ServiceTypeId = h.Id
									AND a.ClientId = f.Id
									AND StartDate >= CURDATE()
							ORDER BY ClientName, StartDate";

                        return $this->getAll($query);
			}				

			//==============================
			// Get Client's Assigment Info
            //==============================			
			function getClientAssigments($ClientId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id as Id,
										a.StatusId,
										AssignmentStatusDesc,
										c.BackgroundColor,
										c.TextColor,
										a.ClientId, 
										a.ClientUnitId, 
										b.UnitName,
										a.Comments,
										a.RegistrantTypeId, 
										a.RegistrantId, 
										COALESCE((SELECT CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) 
											FROM Registrants c
											WHERE a.RegistrantId = c.Id),'To Be Assigned...') as RegistrantName, 
										a.ServiceTypeId,
										ServiceTypeDesc,
										DATE_FORMAT( a.StartDate, '%m-%d-%Y' ) as StartDate,
										DATE_FORMAT( a.EndDate, '%m-%d-%Y' ) as EndDate,
										(Select group_concat( DATE_FORMAT(d.ServiceDate, '%m/%d/%Y' )
                                         SEPARATOR ',' ) 
											FROM WeeklyServices d
											WHERE d.AssignmentId = a.Id
											AND   d.ScheduleStatusId > 5
											AND   DATEDIFF(CURDATE(),d.ServiceDate) < 60 
											GROUP BY AssignmentId) as ExcludedDates,
										
										CONCAT( trim(e.FirstName ) , ' ', trim( e.LastName ) ) as UserName, 
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate	
									FROM 	ClientAssignments a, 
											ClientUnits b, 
											ClientAssignmentStatuses c,
											ServiceTypes h,
											Users e  
									WHERE a.ClientId = '{$ClientId}'	
									AND a.ClientUnitId = b.Id
									AND a.StatusId = c.StatusId
									AND a.StatusId != '3'
									AND a.ServiceTypeId = h.Id									
									AND a.UserId = e.UserId 
									ORDER BY a.StartDate DESC";

                        return $this->getAll($query);
			}	
	
			// Get Shifts Combinations Listing
            //=======================			
			function getShiftsCombinations() {
			
                        $query = "SELECT ShiftsCombinationValues, 
										 ShiftsCombinationDesc 
									FROM ShiftsCombinations";

						return $this->getAll($query);
					}
			//==============================
			// Set Client's Assigment Info
            //==============================			
			function setClientAssigment(	$Id,
											$StatusId,
											$ClientId,
											$ClientUnitId,
											$ServiceTypeId,
											$StartDate,
											$EndDate,
											$ConfirmationNumber,	
											$UserId) {
					
					if(is_numeric($Id))  {
                         $query = "UPDATE ClientAssignments
						            SET StatusId = ?,
                                        ClientId = ?,
										ClientUnitId = ?,
										ServiceTypeId = ?,
										StartDate = ?,
										EndDate = ?,
                                        ConfirmationNumber = ?,
                                        UserId = ?,
                                        TransDate = NOW()
										WHERE ID = ?";

						return $this->execute($query, [$StatusId, $ClientId, $ClientUnitId, $ServiceTypeId, $StartDate, $EndDate, $ConfirmationNumber, $UserId, $Id], 'sssssssss');
					} else {
							$query = "INSERT INTO ClientAssignments (
											   StatusId,
											   ClientId,
											   ClientUnitId,
											   ServiceTypeId,
											   StartDate,
											   EndDate,
											   ConfirmationNumber,
 											   UserId,
											   TransDate)

						       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

						return $this->execute($query, [$StatusId, $ClientId, $ClientUnitId, $ServiceTypeId, $StartDate, $EndDate, $ConfirmationNumber, $UserId], 'ssssssss');
					}
			}				
					
			/* Get Client Assignment Registrants Selection Info
            //================================================ */			
			function getClientAssignmentRegistrantsSelection(	$ClientId,
																$ClientUnitId,
																$ServiceTypeId,
																$StartDate,
																$EndDate
														  ) 
			{
                        								
						$query = "CALL proc_getClientAssignmentRegistrantsSelection(?, ?, ?, ?, ?)";

						return $this->getAll($query, [$ClientId, $ClientUnitId, $ServiceTypeId, $StartDate, $EndDate], 'sssss');
						
			}

			//==============================
			// Set Client's Assigment Registrant Info
            //==============================			
			function setClientAssigmentRegistrant(	$Id,
													$RegistrantId,
													$UserId) {
					
	
                         $query = "UPDATE ClientAssignments
						            SET RegistrantId = ?,
                                         	StatusId = '2',
                                         	UserId = ?,
                                        	TransDate = NOW()
										WHERE ID = ?";

					return $this->execute($query, [$RegistrantId, $UserId, $Id], 'sss');
			}				

			/* Set Add Client Assignment ShiftsData
             ===================================	*/		
			function setClientAssignmentShifts(	$AssignId,
												$Data,
												$UserId) {
					
							
 
					// Match Pedning Shifts with Registrants   
					//===============================================					

					foreach ($Data as $rec) {
								
							$ServiceDate = $rec['ServiceDate'];
							$ShiftId = $rec['ShiftId'];
							//============	
							$dw = date( "w", strtotime( $ServiceDate));
							if ($dw == 6) {
								$we_date = strtotime('Saturday', strtotime($ServiceDate));

							}   else { 
								
								$we_date = strtotime('next Saturday', strtotime($ServiceDate));

							}
	

							$PayrollWeek =  date('Y-m-d', $we_date);	

							//============
							$query = "CALL proc_setClientAssignmentShifts(?, ?, ?, ?, ?)";

							$this->execute($query, [$AssignId, $ServiceDate, $PayrollWeek, $ShiftId, $UserId], 'sssss');
							 	
					} // end of 'foreach' loop
						
				
				return $query;			
			}	

			// Set Client Assignment Schedules Info
            //=======================			
			function getClientAssigmentSchedules($AssignmentId) {
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										ScheduleStatusId, 
										ScheduleStatusDesc,
										TextColor,
										BackgroundColor,
										a.ClientId, 
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										PayrollWeek,  
										LunchHour,
										TotalHours, 
										WeekDay, 
										RegistrantId, 
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName) ,' (', f.RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc, 
										RegistrantTypeId,
										ServiceTypeId,
										ClientUnitId,
										UnitName,
										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											ScheduleStatuses g								
										Where 	a.AssignmentId= '{$AssignmentId}' 
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND ScheduleStatusId = g.Id
											ORDER BY ServiceDate, StartTime";

                        return $this->getAll($query);
						//return $query;  
	 		}	


			/* Get Cncel Client Assignment Info
            //================================================ */			
			function setCancelClientAssignment(	$AssignmentId,
												$Msg,
												$UserId
														  ) 
			{
                        								
						$query = "CALL proc_setCancelClientAssignment(?, ?, ?)";

						return $this->execute($query, [$AssignmentId, $Msg, $UserId], 'sss');
						
			}	

			/* Get the List of Mandatory Credendtiling - Selected (by Registrant Type)
            //============================================================== */			
			function getCredItemsMandatorySelected(	$RegistrantTypeId )
														   
			{
                        								
						$query = "CALL proc_getCredItemsMandatorySelected(?)";

						return $this->getAll($query, [$RegistrantTypeId], 's');
						
			}	

			/* Get the List of Mandatory Credendtiling - Un-Selected (by Registrant Type)
            //============================================================== */			
			function getCredItemsMandatoryUnselected(	$RegistrantTypeId )
														  
			{ 
                        								
						$query = "CALL proc_getCredItemsMandatoryUnselected(?)";

						return $this->getAll($query, [$RegistrantTypeId], 's');
						
			}	
			
			//  Update Mandatory Credentialing Items for selected Registrant Type    
            //==========================================================			
			function setCredItemsMandatory(	$RegistrantTypeId,
											$CredItems,			
											$UserId) 
				{
					
 
                    // Delete All Existing Cred. Items for selected RegistrantGroupId
					//===============================================					
					$query = "DELETE FROM CredentialingItemsMandatory
								WHERE RegistrantTypeId = ?";

					$this->execute($query, [$RegistrantTypeId], 's');

					// Insert newaly Selected Mandatory Cred. Items for selected Registrant Type					//========================================================================					

					foreach ($CredItems as $CredItem) {
							
							$CredItemId = $CredItem['id'];		
							$CondFL = $CredItem['CondFL'];	
										
												
						$query = "INSERT INTO CredentialingItemsMandatory
						   (RegistrantTypeId, MandatoryCredItemId, CondFL, UserId, TransDate)
						VALUES (?, ?, ?, ?, NOW())";

						$this->execute($query, [$RegistrantTypeId, $CredItemId, $CondFL, $UserId], 'ssss');
				
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}				

			/* Get the List of Client's Mandatory Credendtiling Items- Selected (by Client, Client Unit)
            //========================================================================================= */			
			function getCredItemsClientMandatorySelected ($ClientId,  $ServiceTypeId)
														   
			{
                        								
					$query = "CALL proc_getCredItemsClientMandatorySelected1(?, ?)";

					return $this->getAll($query, [$ClientId, $ServiceTypeId], 'ss');
						
			}	

			/* Get the List of Mandatory Credendtiling Items - Un-Selected (by Client, Client Unit)
            //============================================================== */			
			function getCredItemsClientMandatoryUnselected ($ClientId,  $ServiceTypeId)
														  
			{ 
                        								
					$query = "CALL proc_getCredItemsClientMandatoryUnselected1(?, ?)";

					return $this->getAll($query, [$ClientId, $ServiceTypeId], 'ss');
						
			}	

			/* Get List of Mandatory Specilaties - Selected (by Client, Client Unit)
            //========================================================================================= */			
			function getSpecialtiesClientMandatorySelected ($ClientId, $ServiceTypeId)
														   
			{
                        								
					$query = "CALL proc_getSpecialtiesClientMandatorySelected1(?, ?)";

					return $this->getAll($query, [$ClientId, $ServiceTypeId], 'ss');
						
			}	

			/* Get List of Mandatory Specilaties - Un-Selected (by Client, Client Unit)
            //============================================================== */			
			function getSpecialtiesClientMandatoryUnselected (	$ClientId, 
																$ServiceTypeId)
														  
			{ 
                        								
				  $query = "SELECT Id,
									Id as id,
									SpecialtyDesc FROM Specialties a
									WHERE NOT EXISTS (SELECT 1 FROM SpecialtiesClientMandatory b
														WHERE b.ClientId = ?
														AND b.ServiceTypeId = ?
														AND a.Id = b.SpecialtyId)
									ORDER BY a.SpecialtyDesc";

					return $this->getAll($query, [$ClientId, $ServiceTypeId], 'ss');
						
			}				
			// Get Credenting Items Client's Units Listing 
            //=======================			
			function getCredItemsClientUnits($ClientId) {
                        $query = "SELECT ClientId, Id as id, UnitName 
									FROM ClientUnits  
									WHERE ClientId = '{$ClientId}'
									AND StatusId = '1'
									ORDER BY Id";

                        return $this->getAll($query);
						
			}	

			//  Update Mandatory Credentialing Items for selected Client/Unit/Service Type   
            //=================================================================================			
			function setCredItemsClientMandatory(	$ClientId,
													$ServiceTypeId,
													$CredItems,	
													$CredItemsRem,
													$UserId) 
				{
					
 
                    // Delete "Removed" Cred. Items
					//===============================================					
					
					foreach ($CredItemsRem as $CredItemRem) {
							
						$CredItemRemId = $CredItemRem['id'];		

					
						$query = "DELETE FROM CredentialingItemsClientMandatory
									WHERE ClientId = ?
									AND ServiceTypeId = ?
									AND MandatoryCredItemId = ?";

						$this->execute($query, [$ClientId, $ServiceTypeId, $CredItemRemId], 'sss');
							
					}		

					foreach ($CredItems as $CredItem) { // start of 'foreach' loop
							
							$CredItemId = $CredItem['id'];		
							$CondFL = $CredItem['CondFL'];	
							$UnitIds = $CredItem['UnitIds'];
							
						// Delete Cred. Items
						//============================================
						$query = "DELETE FROM CredentialingItemsClientMandatory
									WHERE ClientId = ?
									AND ServiceTypeId = ?
									AND MandatoryCredItemId = ?";

						$this->execute($query, [$ClientId, $ServiceTypeId, $CredItemId], 'sss');

						//============================================

						$units_arr = explode(",", $UnitIds);
	
						for($i=0;$i<count($units_arr);$i++) { //array itr. - start 
							
							$unit_id =  $units_arr[$i];
						
							// Insert Cred. Items
							//============================================
							$query = "INSERT INTO CredentialingItemsClientMandatory
							   (ClientId,
							    ClientUnitId,
								ServiceTypeId,
								MandatoryCredItemId,
								CondFL,
								UserId,
								TransDate)
							SELECT ?, ?, ?, ?, ?, ?, NOW()";

							$this->execute($query, [$ClientId, $unit_id, $ServiceTypeId, $CredItemId, $CondFL, $UserId], 'ssssss');
						

						} //array itr. - end

					
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;			
			}	

			//   Update Mandatory Specialties for selected Client/Unit/Service Type     
            //====================================================================			
			function setSpecialtiesClientMandatory(	$ClientId,
													$ServiceTypeId,
													$Specialties,	
													$SpecialtiesRem,
													$UserId) 
				{
					
                    // Delete "Removed" Specialties
					//===============================================					
					
					foreach ($SpecialtiesRem as $SpecialtieRem) {
							
						$SpecialtieRemId = $SpecialtieRem['id'];		

					
						$query = "DELETE FROM SpecialtiesClientMandatory
									WHERE ClientId = ?
									AND ServiceTypeId = ?
									AND SpecialtyId = ?";

						$this->execute($query, [$ClientId, $ServiceTypeId, $SpecialtieRemId], 'sss');
							
					}		

					foreach ($Specialties as $Specialty) { // start of 'foreach' loop
							
							$SpecialtyId = $Specialty['id'];		
							$UnitIds = $Specialty['UnitIds'];
							
						// Delete Specialties
						//============================================
						$query = "DELETE FROM SpecialtiesClientMandatory
									WHERE ClientId = ?
									AND ServiceTypeId = ?
									AND SpecialtyId = ?";

						$this->execute($query, [$ClientId, $ServiceTypeId, $SpecialtyId], 'sss');

						//============================================

						$units_arr = explode(",", $UnitIds);
	
						for($i=0;$i<count($units_arr);$i++) { //array itr. - start 
							
							$unit_id =  $units_arr[$i];
						
							// Insert Specialties
							//============================================
							$query = "INSERT INTO SpecialtiesClientMandatory
							   (ClientId,
							    ClientUnitId,
								ServiceTypeId,
								SpecialtyId,
								UserId,
								TransDate)
							SELECT ?, ?, ?, ?, ?, NOW()";

							$this->execute($query, [$ClientId, $unit_id, $ServiceTypeId, $SpecialtyId, $UserId], 'sssss');
						

						} //array itr. - end

					
					
					
					} // end of 'foreach' loop
						
				//return $result;
				return $query;	
			}		
			
			/*Set Update Registrant's Client Required Credendtiling Items  
            //============================================================== */			
			function setUpdateRegistrantsClientRequiredCredItems ($UserId)
														  
			{ 
                        								
					$query = "CALL proc_UpdateRegistrantsClientRequiredCredItems(?)";

					return $this->getAll($query, [$UserId], 's');
						
			}	
			

			/*Set Update Fegistrant's Mandatory Credendtiling Items  
            //============================================================== */			
			function setUpdateRegistrantsMandatoryCredItems ($RegistrantTypeId, $UserId)
														  
			{ 
                        								
					$query = "CALL proc_UpdateRegistrantsMandatoryCredItems(?, ?)";

					return $this->getAll($query, [$RegistrantTypeId, $UserId], 'ss');
						
			}	


			/* Get the List of Specialties Selected (by Registrant Type)
            //============================================================== */			
			function getSpecialtiesByRegistrantTypeSelected(	$RegistrantTypeId )
														  
			{ 
                        								
                       $query = "SELECT a.Id as id,
											a.SpecialtyDesc
									FROM Specialties a
									WHERE EXISTS (SELECT 1 FROM RegistrantTypeSpecialties b
													WHERE b.RegistrantTypeId = ?
													AND a.id = b.SpecialtyId)
									ORDER BY a.SpecialtyDesc";

                        return $this->getAll($query, [$RegistrantTypeId], 's');
						
			}	
			

			/* Get the List of Specialties Un-Selected (by Registrant Type)
            //============================================================== */			
			function getSpecialtiesByRegistrantTypeUnselected(	$RegistrantTypeId )
														  
			{ 
                        								
                       $query = "SELECT a.Id as id,
											a.SpecialtyDesc
									FROM Specialties a
									WHERE NOT EXISTS (SELECT 1 FROM RegistrantTypeSpecialties b
													WHERE b.RegistrantTypeId = ?
													AND a.id = b.SpecialtyId)
									ORDER BY a.SpecialtyDesc";

                        return $this->getAll($query, [$RegistrantTypeId], 's');
						
			}	

			/* Get List of Mandatory Specilaties - Selected (by Client, Client Unit)
            //========================================================================================= */			
			function getRegistrantClientRequiredCredItems (	$RegistrantId,
															$ClientId,
															$ClientUnitId,
															$ServiceTypeId)
														   
			{
                        								
					$query = "CALL proc_getRegistrantClientRequiredCredItems(?, ?, ?, ?)";

					return $this->getAll($query, [$RegistrantId, $ClientId, $ClientUnitId, $ServiceTypeId], 'ssss');
						
			}	

			/* Set List of Mandatory Specilaties - Selected (by Client, Client Unit)
            //========================================================================================= */			
			function setRegistrantRequiredCredItems ($RegistrantId, $UserId)
														   
			{
                        								
					$query = "CALL proc_setRegistrantRequiredCredItems(?, ?)";

					return $this->getAll($query, [$RegistrantId, $UserId], 'ss');
						
			}	

			
			// Get Mesage Types  Listing
            //=====================================			
			function getMessageTypes($MessageGroupId) {
			
                        $query = "SELECT Id as id,
										 Id as MessageTypeId,
                                         MessageTypeDesc
									FROM MessageTypes
									WHERE MessageGroupId = ?
									ORDER BY MessageTypeDesc";

 						return $this->getAll($query, [$MessageGroupId], 's');
						 

			}

			// Get Registrant Messages
            //=======================			
		 	function getRegistrantMessages($RegistrantId) {
			
                        $query = "SELECT a.Id as MsgId,
								Msg,
								HighPriority,

								CASE HighPriority
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,

								RegistrantId,
								MessageTypeId,
								MessageTypeDesc,
								CONCAT(TRIM(b.FirstName), ' ', TRIM(b.LastName)) AS UserName,
								DATE_FORMAT(a.TransDate, '%m-%d-%Y %h:%i %p') as TransDate
							FROM RegistrantMessages a, Users b, MessageTypes c
								WHERE RegistrantId = ?
								AND a.MessageTypeId = c.Id
								AND a.UserId = b.UserId
								ORDER BY a.TransDate DESC";

						return $this->getAll($query, [$RegistrantId], 's');
			}				

			/* Set Registrant's Message */
            /*======================= */			
			function setRegistrantMessages( $RegistrantId,
											$HighPriority,
											$MessageTypeId,
											$Msg,
											$UserId )  
			{

                       	$query = "INSERT INTO RegistrantMessages
								(RegistrantId,
								HighPriority,
								MessageTypeId,
								Msg,
								UserId,
								TransDate)
				VALUES (?, ?, ?, ?, ?, NOW())";

						return $this->execute($query, [$RegistrantId, $HighPriority, $MessageTypeId, $Msg, $UserId], 'sssss');
						//return $query;
						
			}

			// Get Dasboard Registrants Inquiry  Messages
            //================================================			
		 	function RegistrantsMessagesByType(	$MessageTypeId,
												$FromMessageDate,
												$ToMessageDate) {
			
                        $query = "SELECT c.Id as RegistrantId,
											CONCAT(TRIM(c.LastName), ', ', TRIM(c.FirstName), ' (', RegistrantTypeDesc, ')', ' (', c.Id, ')') as RegistrantName,
											Msg,
											CONCAT(TRIM(a.FirstName), ' ', TRIM(a.LastName)) AS UserName,
											DATE_FORMAT(b.TransDate, '%m-%d-%Y %r') as TransDate

									FROM Registrants c,
											RegistrantTypes f,
											RegistrantMessages b,
											Users a
									WHERE c.TypeId = f.Id
									AND c.StatusId = '1'
									AND c.Id = b.RegistrantId
									AND MessageTypeId = ?
									AND b.TransDate BETWEEN ? AND ?
									AND b.UserId = a.UserId
									ORDER BY RegistrantName";

						return $this->getAll($query, [$MessageTypeId, $FromMessageDate, $ToMessageDate], 'sss');
			}				

			// Get Service Cancellation Reasons Listing
            //=====================================			
			function ServiceCancellationReasons($ServiceCancellationReasonTypeId) {
			
                        $query = "SELECT Id as id,
										 Id as ServiceCancellationReasonId,
                                         ServiceCancellationReasonDesc
									FROM ServiceCancellationReasons
									WHERE ServiceCancellationReasonTypeId = ?
									ORDER BY Id";

 						return $this->getAll($query, [$ServiceCancellationReasonTypeId], 's');
						 

			}

			//=====================================================
			// Get Client's Service Cancellations History Info
            //=====================================================			
			function getClientServiceCancellations($ClientId) {
			
                        	$query = "SELECT
                            			COALESCE((SELECT CONCAT(TRIM(d.LastName), ', ', TRIM(d.FirstName), ' (', RegistrantTypeDesc, ')')
                         					FROM Registrants d, RegistrantTypes c
											WHERE f.RegistrantId = d.Id
                            				AND Typeid = c.Id
                         				), '') AS RegistrantName,
 										DATE_FORMAT(ServiceDate, '%m-%d-%Y') as ServiceDate,
										CancelReason,
										COALESCE((SELECT ServiceCancellationReasonDesc
												FROM ServiceCancellationReasons e
												WHERE e.id = ServiceCancellationReasonId
											), 'Undefined') as ServiceCancellationReasonDesc,
										CONCAT(TRIM(b.FirstName), ' ', TRIM(b.LastName)) AS UserName,
										DATE_FORMAT(a.TransDate, '%m-%d-%Y %r') as TransDate
									FROM ClientServiceCancellations a,
                                            WeeklyServices f,
 											Users b
									WHERE a.UserId = b.UserId
                                            AND a.ScheduleId = f.Id
                                            AND a.ClientId = ?
                                        ORDER BY a.TransDate DESC";

						return $this->getAll($query, [$ClientId], 's');
						
			}	

			/* Get List of Mandatory Specilaties for give Client/Service Type/Client Unit
            //============================================================== */			
			function getClientServiceTypeReqSpecialties (	$ClientId, 
															$ServiceTypeId,
															$ClientUnitId)
														  
			{ 
                        								
				  $query = "SELECT b.Id as id, SpecialtyDesc
							FROM SpecialtiesClientMandatory a, Specialties b
							 WHERE ClientId = ?
							 AND ServiceTypeId = ?
							 AND ClientUnitId IN (0, ?)
							 AND b.id = a.SpecialtyId
							 ORDER BY SpecialtyDesc";

					return $this->getAll($query, [$ClientId, $ServiceTypeId, $ClientUnitId], 'sss');
						
			}

          /* Set User Password  
          //=======================	*/		
			function setUserPassword(	$CurrPassword,
										$NewPassword, 
										$UserId) {
			
                        $query = "UPDATE Users
								SET Password = ?,
								    NextResetDate = DATE_ADD(CURDATE(), INTERVAL 90 DAY),
									ResetFL = 0
							WHERE UserId = ?
									AND Password = ?";

						return $this->execute($query, [$NewPassword, $UserId, $CurrPassword], 'sss');
						
			}	

			/* Get Daily Messages
            ======================= */			
		 	function getDailyMessages($MessageDate) {
			
                        $query = "SELECT Id as MsgId,
								Msg,
								HighPriority,

								CASE HighPriority
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,
								CONCAT(TRIM(b.FirstName), ' ', TRIM(b.LastName)) AS UserName,
								DATE_FORMAT(a.TransDate, '%m-%d-%Y %h:%i %p') as TransDate
							FROM DailyMessages a, Users b
								WHERE MessageDate = ?
								AND a.UserId = b.UserId
								ORDER BY a.TransDate DESC";

						return $this->getAll($query, [$MessageDate], 's');
			}	
			
			/* Set New Daily Message  
            ======================= */			
			function setDailyMessage   ($MessageDate,
										$HighPriority,
										$Msg,
										$UserId )  
			{
			
		                       	$query = "INSERT INTO DailyMessages
								(MessageDate,
								HighPriority,
								Msg,
								UserId,
								TransDate)
				VALUES (?, ?, ?, ?, NOW())";

						return $this->execute($query, [$MessageDate, $HighPriority, $Msg, $UserId], 'ssss');
						//return $query;
						
			}	

		    //====================================			
			// Get Un-Apporoved Registrants	Info
			//===================================
			
			function getUnApprovedRegistrants(	$FromDate, 
												$ToDate)

			{
						$query ="SELECT	c.Id,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc,
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate,

										CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										(Select group_concat( CredItemDesc
                                        		 SEPARATOR ', ' ) 
					                    FROM RegistrantCredItems k,  CredentialingItems h
					                        WHERE  c.Id  = k.RegistrantId 
			                                AND  k.CredItemId = h.Id
			                                AND CredItemStatus in (1,3)) as ExpiredCredItems,
										(SELECT  CASE count(*) 
			                                                    			WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
												END as SpecialtiesList
                                            			FROM RegistrantAttchedSpecialties f, Specialties g
                                            				where g.Id = f.SpecialtyId
                                                            and  c.Id = f.RegistrantId) as SpecialtiesList,
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate
								FROM 	
										Registrants c,
										RegistrantTypes f,
										Users b
													
										WHERE 	c.TypeId = f.Id
										AND     c.UserId = b.UserId
										AND     c.StatusId = '1' 	 
												AND NOT EXISTS (SELECT 1 FROM ClientApprovedRegistrants d
								                               WHERE c.Id = d.RegistrantId
								                              
														) 
										AND c.EntryDate BETWEEN ? AND ?
										ORDER BY c.EntryDate, RegistrantName";

						return $this->getAll($query, [$FromDate, $ToDate], 'ss');
			

			}			

			/* Get Alert Messages
            ======================= */			
		 	function getAlertMessages() {
			
                        $query ="SELECT Id as AlertId ,
								Msg,
								StatusId,
								0 as  Seq,
								'Orig' as AlertType,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b  
								WHERE a.UserId = b.UserId 
								
						UNION								
								SELECT c.AlertId ,
								c.Msg,
								a.StatusId,
								c.Id as Seq,
								'Resp' as AlertType,
								CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( c.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b, AlertMessageResponses c  
								WHERE a.Id = c.AlertId   
								AND c.UserId = b.UserId 

								ORDER BY StatusId, SortTransDate DESC, AlertId DESC, Seq";

						return $this->getAll($query);
			}	

			/* Set Alert Message Responce  
            ============================================== */			
			function setAlertMessageResponse   ($AlertId,
												$Msg,
												$SchoolFL,
												$UserId )  
			{
			
		                       	$query = "INSERT INTO AlertMessageResponses
								(AlertId,
								Msg,
								SchoolFL,
								UserId,
								TransDate)
				VALUES (?, ?, ?, ?, NOW())";

						return $this->execute($query, [$AlertId, $Msg, $SchoolFL, $UserId], 'ssss');
						//return $query;
						
			}	

			/* Set Close Alert Message  
            ============================================== */			
			function setCloseAlertMessage (	$AlertId,
											$UserId )  
			{
			
                       	$query = "UPDATE AlertMessages
                       				SET StatusId = '1'
                       			  WHERE Id = ?";

						return $this->execute($query, [$AlertId], 's');
						//return $query;
						
			}	

			/* Set New Alert Message  
            ============================================== */			
			function setNewAlertMessage (	$Msg,
											$SchoolFL,
											$UserId )  
			{
			
                   	$query = "INSERT INTO AlertMessages
                   				(StatusId, Msg, SchoolFL, UserId, TransDate)
							  VALUES ('0', ?, ?, ?, NOW())";

					return $this->execute($query, [$Msg, $SchoolFL, $UserId], 'sss');
					//return $query;
						
			}	

		    //================================================			
			// Get Apporoved Registrants by Entry Date	Info
			//================================================
			
			function getApprovedRegistrantsByEntryDates($FromDate, $ToDate) {
						$query ="SELECT	c.Id,
										CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName,
										RegistrantTypeDesc,
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate,

										CONCAT ( trim(MobilePhone) , ' (M) ',  trim(HomePhone), ' (H) ') as PhoneNumbers,
										CONCAT( trim( b.FirstName ) , ' ', trim( b.LastName ) ) AS UserName,
										(Select group_concat( CredItemDesc
                                        		 SEPARATOR ', ' ) 
					                    FROM RegistrantCredItems k,  CredentialingItems h
					                        WHERE  c.Id  = k.RegistrantId 
			                                AND  k.CredItemId = h.Id
			                                AND CredItemStatus in (1,3)) as ExpiredCredItems,
										(SELECT  CASE count(*) 
			                                                    			WHEN 0 THEN ''
												ELSE group_concat( SpecialtyDesc SEPARATOR ', ' )
												END as SpecialtiesList
                                            			FROM RegistrantAttchedSpecialties f, Specialties g
                                            				where g.Id = f.SpecialtyId
                                                            and  c.Id = f.RegistrantId) as SpecialtiesList,
										(Select group_concat( ClientName
                                        		 SEPARATOR ', ' ) 
					                    FROM ClientApprovedRegistrants k,  Clients h
					                        WHERE  c.Id  = k.RegistrantId 
			                                AND  k.ClientId = h.Id ) as ApprovedByClients,
			                                
										DATE_FORMAT( c.EntryDate, '%m-%d-%Y %h:%i %p' ) as EntryDate
								FROM 	
										Registrants c,
										RegistrantTypes f,
										Users b
													
										WHERE 	c.TypeId = f.Id
										AND     c.UserId = b.UserId
										AND     c.StatusId = '1' 	 
										AND EXISTS (SELECT 1 FROM ClientApprovedRegistrants d
								                               WHERE c.Id = d.RegistrantId
								                              
														)
										AND c.EntryDate BETWEEN ? AND ?
										ORDER BY c.EntryDate, RegistrantName";
                                                      
                        return $this->getAll($query, [$FromDate, $ToDate], 'ss');


			}			


			/* Set Change Registrant Type Credentialing Items
            //======================= */ 			
			function setRegistrantTypeCredItemsChange (	$RegistrantId, 
														$SourceTypeId,
														$DestTypeId,
														$UserId) {
			
 
							
                        $query = "CALL proc_setRegistrantTypeCredItemsChange(?, ?, ?, ?)";
                        return $this->getAll($query, [$RegistrantId, $SourceTypeId, $DestTypeId, $UserId], 'ssss');
						
			}	


			/* Get Registrants Daily Availability
            ========================================== */			
		 	function getRegistrantsDailyAvailibility($ClientId, $ServiceDate) {
			
                        $query ="SELECT 	CONCAT( trim( c.LastName) , ', ', trim( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName, 
											UnitName,
											ServiceTypeDesc,
											ShiftName,
											COALESCE(CONCAT ( trim(MobilePhone) , ' ',  trim(HomePhone), ' ', trim(c.Email)),'') as PhoneNumbers,
											
											(Select group_concat( CredItemDesc
									                 SEPARATOR ', ' ) 
									        FROM    RegistrantCredItems k,  
									                CredentialingItems g	
									            WHERE  c.Id  = k.RegistrantId 
									            AND  k.CredItemId = g.Id
									            AND ComplianceLevelId = 1
									            AND ((g.CredItemCategory  !='2') || 
									                ((g.CredItemCategory  = '2') && (k.StatusId != '0')))) as ExpiredCredItems
 


									FROM  RegistrantVerifiedAvailability a,
									      ClientApprovedRegistrantsNonRest b,
									      Registrants c,
									      RegistrantTypes f,
									      ClientUnits e,
										  ServiceTypes h,
										  Shifts d	

									WHERE b.ClientId = ?
									AND a.RegistrantId = b.RegistrantId
									AND a.RegistrantId = c.Id
									AND c.TypeId = f.Id
									AND a.ServiceDate = ?
									AND b.ClientUnitId = e.Id
									AND b.ServiceTypeId = h.Id
									AND a.ShiftId = d.ShiftId
									AND a.ShiftMatchedFL = '0'
									AND NOT EXISTS (SELECT 1 FROM WeeklyServices i
										              WHERE a.RegistrantId = i.RegistrantId
										              AND i.ServiceDate = ?
										              AND a.ShiftId = i.ShiftId)
									ORDER BY a.ShiftId, c.LastName, c.FirstName";

                        return $this->getAll($query, [$ClientId, $ServiceDate, $ServiceDate], 'sss');

			}	



			// Get School Districts Side Navigation Data
            //==============================================			
			function getSchDistrictSideNavigation() {
			
                        $query = "SELECT *							
						FROM SchDistrictSideNavigation 
						ORDER BY id  ";
                                                      
                        return $this->getAll($query);
						
			}


			
			/* Get Districts Listing
            //======================= */			
			function getSchDistricts() {
                        $query = "SELECT Id as id, 
										Id, 
										DistrictName,
										SearchId,
										ExtId as BillingClientCode
							FROM SchDistricts 
					order by DistrictSort  ";
                                                      
                        return $this->getAll($query);
						
			}



			/* Get Selected (SCH) District's General Info  
            /*=============================================== */			
			
			function getSelSchDistrict($DistrictId) {
			
                        $query ="SELECT a.Id as id, 
										a.Id, 
										ExtId,
										SearchId,
										StatusId,
										DistrictName,
										BoroughId,
										StreetAddress1,
										StreetAddress2,
										City,
										State,
										ZipCode,
										LiaisonFirstName,
										LiaisonLastName,
										OfficePhone,
										MobilePhone,
										Fax,
										Email,
										Comments,
										PerDiemBillingCode,
										LongTermBillingCode,
										UserId,
										TransDate
									FROM SchDistricts a  
									WHERE Id = ?";

                        return $this->getAll($query, [$DistrictId], 's');
						
			}			


			/* Get Boroughs (Sch) Listing
            //=======================	*/		
			function getSchBoroughs($State) {
			
                        $query = "SELECT 	Id as id, 
											Id as BoroughId, 	
											BoroughName		
								FROM  SchBoroughs 
								WHERE State = ?
							ORDER BY BoroughName";

                        return $this->getAll($query, [$State], 's');
						
			}



			/* Set (SCH) Selected District's General Info */
            /*========================================= */
			function setSelSchDistrict( $Id,
										$ExtId,
										$StatusId,
										$SearchId,
										$DistrictStatusId,
										$DistrictName,
										$BoroughId,
										$StreetAddress1,
										$City,
										$State,
										$ZipCode,
										$LiaisonFirstName,
										$LiaisonLastName,
										$OfficePhone,
										$MobilePhone,
										$Fax,
										$Email,
										$Comments,
										$PerDiemBillingCode,
										$LongTermBillingCode,
										$UserId )
			{


				if(is_numeric($Id) ) {

                        $query ="UPDATE SchDistricts
								SET ExtId = ?,
								StatusId = ?,
								SearchId = ?,
								DistrictName = ?,
								BoroughId = ?,
								StreetAddress1 = ?,
								City = ?,
								State = ?,
								ZipCode = ?,
								LiaisonFirstName = ?,
								LiaisonLastName = ?,
								OfficePhone = ?,
								MobilePhone = ?,
								Fax = ?,
								Email = ?,
								Comments = ?,
								PerDiemBillingCode = ?,
								LongTermBillingCode = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

						$this->execute($query, [$ExtId, $StatusId, $SearchId, $DistrictName, $BoroughId, $StreetAddress1, $City, $State, $ZipCode, $LiaisonFirstName, $LiaisonLastName, $OfficePhone, $MobilePhone, $Fax, $Email, $Comments, $PerDiemBillingCode, $LongTermBillingCode, $UserId, $Id], 'ssssssssssssssssssss');
                } else {
                       $query ="INSERT INTO SchDistricts
								(ExtId,
								StatusId,
								SearchId,
								DistrictName,
								BoroughId,
								StreetAddress1,
								City,
								State,
								ZipCode,
								LiaisonFirstName,
								LiaisonLastName,
								OfficePhone,
								MobilePhone,
								Fax,
								Email,
								Comments,
								PerDiemBillingCode,
								LongTermBillingCode,
								UserId,
								TransDate )
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

						$this->execute($query, [$ExtId, $StatusId, $SearchId, $DistrictName, $BoroughId, $StreetAddress1, $City, $State, $ZipCode, $LiaisonFirstName, $LiaisonLastName, $OfficePhone, $MobilePhone, $Fax, $Email, $Comments, $PerDiemBillingCode, $LongTermBillingCode, $UserId], 'sssssssssssssssssss');
				}

						//return $result;
						return $query;
			}


			// Get (SCH) District Messages
            //=========================================			
		 	function getSchDistrictMessages($DistrictId) {

                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,

								CASE HighPriority
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,

								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM SchDistrictMessages a, Users b
								WHERE DistrictId = ?
								AND a.UserId = b.UserId
								ORDER BY  a.TransDate DESC";

                        return $this->getAll($query, [$DistrictId], 's');
			}				

			/* Set (SCH) District Message */
            /*======================= */			
			function setSchDistrictMessages($DistrictId,
											$HighPriority,
											$Msg,
											$UserId )
			{

                       $query ="INSERT INTO SchDistrictMessages
								(DistrictId,
								HighPriority,
								Msg,
								UserId,
								TransDate )
				VALUES 	(?,
						?,
						?,
						?,
						NOW()  ) ";

						return $this->execute($query, [$DistrictId, $HighPriority, $Msg, $UserId], 'ssss');
						
			}	


			/* Get (SCH) District's Services Details Info */
            /*================================================= */			
			function getSchDistrictServiceDetails ($DistrictId) {

                        $query ="SELECT a.Id,
										b.Id as ServiceTypeId,
										b.ServiceTypeDesc,

										CASE b.ServiceCategoryId
								        	WHEN '0' THEN 'School'
								        	ELSE 'Student'
								        END AS 'ServiceCategoryDesc',

								        CASE b.RegistrantTypeId
								        	WHEN '12' THEN 'RN'
								        	WHEN '23' THEN 'Para'
								        	WHEN '7'  THEN 'PT'
								        	WHEN '17'  THEN 'OT'
								        	WHEN '16'  THEN 'ST'

										END AS 'RegistrantTypeDesc',
										COALESCE(PrimaryVendorFL,'0') as PrimaryVendorFL,
										COALESCE(PayRate, '0.00') as PayRate,
										COALESCE(BillRate, '0.00') as BillRate,
										COALESCE(CONCAT( TRIM( c.FirstName ) , ' ', TRIM( c.LastName )),'') as UserName,
										COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%m:%s' ),'') as TransDate

									FROM SchDistrictServiceDetails a

										RIGHT JOIN SchServiceTypes b
											ON a.ServiceTypeId = b.Id
											AND a.DistrictId = ?
										LEFT JOIN Users c
											ON a.Userid = c.Userid
									ORDER BY ServiceCategoryId, RegistrantTypeId
											";

                        return $this->getAll($query, [$DistrictId], 's');
						
			}	

			/* Set District's Services Details Info */
            /*======================= */			
			function setSchDistrictServiceDetails(	$DistrictId,
													$ServiceId,
													$ServiceTypeId,
													$PrimaryVendorFL,
													$PayRate,
													$BillRate,
													$UserId )
			{


				if(is_numeric($ServiceId))  {

                        $query =" UPDATE SchDistrictServiceDetails
									SET PrimaryVendorFL =  ?,
										PayRate =  ?,
										BillRate =  ?,
										UserId =  ?,
										TransDate = NOW()
									WHERE Id = ?";

					return $this->execute($query, [$PrimaryVendorFL, $PayRate, $BillRate, $UserId, $ServiceId], 'sssss');
  				} else {

  						$query =" INSERT INTO SchDistrictServiceDetails
										(
										DistrictId,
										ServiceTypeId,
										PrimaryVendorFL,
										PayRate,
										BillRate,
										UserId,
										TransDate )
										VALUES
										(
										 ?,
										 ?,
										 ?,
										 ?,
										 ?,
										 ?,
										 NOW()
										 )";

					return $this->execute($query, [$DistrictId, $ServiceTypeId, $PrimaryVendorFL, $PayRate, $BillRate, $UserId], 'ssssss');
  				}
						
			}	


			//============================================
			// Get Payroll Open Transactions Info
            //============================================			
			function getPayrollOpenTransactions() 	{
				 
                       $query ="SELECT  a.Id AS ScheduleId, 
										SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,
										SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
										g.ExtId as OverrideBillingCode,
										DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate, 
										StartTime as StartTimeNum,
										EndTime as EndTimeNum,
										DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
										DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
										DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek, 
										DATE_FORMAT( ServiceDate, '%m%d%Y' ) as ServiceDateUpload,
										DATE_FORMAT( StartTime, '%h%i%p' ) as StartTimeUpload, 
										DATE_FORMAT( EndTime, '%h%i%p' ) as EndTimeUpload,
										
										ABS(TotalHours) as TotalHours,
										WeekDay, 
										RegistrantId, 
										b.ExtId as EmplId,
										CONCAT( trim( b.LastName) , ', ', trim( b.FirstName)) as RegistrantName ,  
										b.LastName,
										b.FirstName,
										
										CASE NextDayPay 
											WHEN '1' THEN 'Next Day Pay'
											ELSE ''
										END AS NextDayPayFL, 
										RegistrantTypeDesc, 
										ClientName,
										ClientUnitId,
										UnitName,
										ServiceTypeDesc,

										CONCAT( trim( e.FirstName) , ' ', trim( e.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a, 
											Registrants b, 
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											Clients d,
											ServiceTypes g 							
										  	
											WHERE	ScheduleStatusId = 8
												AND a.PayrollBatchNumber = ''
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id 
												AND a.ClientId = d.Id
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND a.ServiceTypeId = g.Id
												AND c.ExtId != ''
										ORDER BY b.LastName, b.FirstName, ServiceDate, StartTime 	
											";

                        return $this->getAll($query);
	 		}			

			//============================================
			// Get Payroll Upload Batch Headers Info
            //============================================			
			function getPayrollUploadBatchHeaders() 	{
				 
                       $query ="SELECT  a.Id AS PayrollBatchNumber, 
										DATE_FORMAT( BatchDate, '%m-%d-%Y' ) AS BatchDate, 
										BatchCount,
									    CONCAT( trim( b.FirstName) , ' ', trim( b.LastName)) as UserName ,  
										DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	PayrollBatchHeader a, 
											Users b
									WHERE   a.UserId = b.UserId		
									AND 	a.Id != '0'
									AND EXISTS (SELECT  1 FROM WeeklyServices b 
									                WHERE a.Id = b.PayrollBatchNumber
									            )
									ORDER BY PayrollBatchNumber DESC  
									";

                        return $this->getAll($query);
	 		}			


			//============================================
			// Get Payroll Upload Batch Details Info
            //============================================			
			function getPayrollUploadBatchDetails($PayrollBatchNumber) 	{

                   $query ="SELECT  a.Id AS ScheduleId,
									DATE_FORMAT( ServiceDate, '%m-%d-%Y' ) AS ServiceDate,
									StartTime as StartTimeNum,
									EndTime as EndTimeNum,
									DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
									DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
									DATE_FORMAT( PayrollWeek, '%m-%d-%Y' ) AS PayrollWeek,
									TotalHours,
									WeekDay,
									RegistrantId,
									CONCAT( TRIM( b.LastName) , ', ', TRIM( b.FirstName)) as RegistrantName,
									RegistrantTypeDesc,
									ClientName,
									ClientUnitId,
									UnitName,
									ServiceTypeDesc,
									CASE NextDayPay
											WHEN '1' THEN 'Next Day Pay'
											ELSE ''
										END AS NextDayPayFL,
									CONCAT( TRIM( e.FirstName) , ' ', TRIM( e.LastName)) as UserName ,
									DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) AS TransDate
									FROM 	WeeklyServices a,
											Registrants b,
											RegistrantTypes f,
											Users e,
											ClientUnits c,
											Clients d,
											ServiceTypes g

											WHERE	PayrollBatchNumber = ?
												AND a.RegistrantId = b.Id
												AND b.TypeId = f.Id
												AND a.ClientId = d.Id
												AND a.UserId = e.UserId
												AND a.ClientUnitId = c.Id
												AND a.ServiceTypeId = g.Id
										ORDER BY b.LastName, b.FirstName, ServiceDate, StartTime
											";

                        return $this->getAll($query, [$PayrollBatchNumber], 's');
	 		}			

			// Get School (SCH) Side Navigation Data
            //==============================================
			function getSchSchoolSideNavigation() {

                        $query = "SELECT *
						FROM SchSchoolSideNavigation
						ORDER BY id  ";

                        return $this->getAll($query);
						
			}

			/* Get (SCH) Schools Listing
            //======================= */			
			function getSchSchools ($DistrictId) {


				if(!is_numeric($DistrictId)) {

                        $query = "SELECT 	a.Id as id,
											a.Id,
											CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId

								FROM        SchSchools a,
											SchDistricts b
								WHERE       a.DistrictId = b.Id

							    ORDER BY    TRIM(SchoolName)  ";

					return $this->getAll($query);

				} else {

                        $query = "SELECT 	a.Id as id,
											a.Id,
											CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
											a.SearchId,
											b.Id as DistrictId,
											a.ExtId as DoeId

								FROM        SchSchools a,
											SchDistricts b
								WHERE       b.Id = ?
								AND         a.DistrictId = b.Id

							    ORDER BY    TRIM(SchoolName)  ";

					return $this->getAll($query, [$DistrictId], 's');

				}
						//return $query;
			}

			/* Get Selected School's General Info */
            /*======================= */			
			function getSelSchSchool($SchoolId) {

                        $query ="SELECT a.Id as id,
										a.Id,
										a.ExtId,
										a.SearchId,
										a.StatusId,
										SchoolName,
										CONCAT(TRIM(SchoolName),' (',DistrictName,') ') as SchoolNameDisp,
										SchoolTypeId,
										SchoolTypeDesc,
										DistrictId,
										a.StreetAddress1,
										a.StreetAddress2,
										a.City,
										a.State,
										a.ZipCode,
										a.LiaisonFirstName,
										a.LiaisonLastName,
										a.OfficePhone,
										a.MobilePhone,
										a.Fax,
										a.Email,
										a.Comments,
										a.UserId,
										a.TransDate

									FROM 	SchSchools a,
											SchSchoolTypes b,
											SchDistricts c
										where a.Id = ?
										AND a.SchoolTypeId = b.Id
										AND a.DistrictId = c.Id ";

                        return $this->getAll($query, [$SchoolId], 's');
						
			}	
			
			/* Set Selected School's General Info */
            /*======================= */			
			function setSelSchSchool(  	$Id,
										$ExtId,
										$SearchId,
										$DistrictId,
										$StatusId,
										$SchoolName,
										$SchoolTypeId,
										$StreetAddress1,
										$City,
										$State,
										$ZipCode,
										$LiaisonFirstName,
										$LiaisonLastName,
										$OfficePhone,
										$MobilePhone,
										$Fax,
										$Email,
										$Comments,
										$UserId )  
			{
	

				if(is_numeric($Id) ) {

                        $query ="UPDATE SchSchools
								SET ExtId = ?,
								DistrictId = ?,
								StatusId = ?,
								SchoolName = ?,
								SchoolTypeId = ?,
								StreetAddress1 = ?,
								City = ?,
								State = ?,
								ZipCode = ?,
								LiaisonFirstName = ?,
								LiaisonLastName = ?,
								OfficePhone = ?,
								MobilePhone = ?,
								Fax = ?,
								Email = ?,
								Comments = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

					return $this->execute($query, [$ExtId, $DistrictId, $StatusId, $SchoolName, $SchoolTypeId, $StreetAddress1, $City, $State, $ZipCode, $LiaisonFirstName, $LiaisonLastName, $OfficePhone, $MobilePhone, $Fax, $Email, $Comments, $UserId, $Id], 'ssssssssssssssssss');

                } else {
                       $query ="INSERT INTO SchSchools
								(ExtId,
								SearchId,
								DistrictId,
								StatusId,
								SchoolName,
								SchoolTypeId,
								StreetAddress1,
								City,
								State,
								ZipCode,
								LiaisonFirstName,
								LiaisonLastName,
								OfficePhone,
								MobilePhone,
								Fax,
								Email,
								Comments,
								UserId,
								TransDate )
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

					return $this->execute($query, [$ExtId, $SearchId, $DistrictId, $StatusId, $SchoolName, $SchoolTypeId, $StreetAddress1, $City, $State, $ZipCode, $LiaisonFirstName, $LiaisonLastName, $OfficePhone, $MobilePhone, $Fax, $Email, $Comments, $UserId], 'ssssssssssssssssss');

				}
						
			}				

			/* Get (SCH )School Types Listing
            //=======================	*/		
			function getSchSchoolTypes() {

                        $query = "SELECT Id as id,
										Id as SchoolTypeId,
										SchoolTypeDesc
										FROM SchSchoolTypes
								Order by Id   ";

                        return $this->getAll($query);
						
			}	


			// Get (SCH) School Messages
            //=========================================			
		 	function getSchSchoolMessages($SchoolId) {

                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,

								CASE HighPriority
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,

								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM SchSchoolMessages a, Users b
								WHERE SchoolId = ?
								AND a.UserId = b.UserId
								Order By  a.TransDate Desc";

                        return $this->getAll($query, [$SchoolId], 's');
			}				

			/* Set (SCH) School Message */
            /*======================= */			
			function setSchSchoolMessages  ($SchoolId,
											$HighPriority,
											$Msg,
											$UserId )
			{

                       $query ="INSERT INTO SchSchoolMessages
								(SchoolId,
								HighPriority,
								Msg,
								UserId,
								TransDate )
				VALUES (?, ?, ?, ?, NOW())";

						return $this->execute($query, [$SchoolId, $HighPriority, $Msg, $UserId], 'ssss');
						
			}	

			// Get (SCH) School Assignment Headers
            //=========================================
		 	function getSchSchoolAssignmentHeaders($SchoolId) {

                        $query ="SELECT a.Id as Id ,
								a.Id,
								a.SchoolId,
								a.StatusId,
								ServiceTypeId as OrigServiceTypeId,
								ServiceTypeId,
								ServiceTypeDesc,
								RegistrantTypeId,
                                RegistrantTypeDesc,
								AssignmentTypeId,
								CASE AssignmentTypeId
									WHEN '1' THEN 'Long Term'
										ELSE 'Per Diem'
								END AS AssignmentTypeDesc,
								ConfirmationNumber,
								DATE_FORMAT( StartDate, '%m-%d-%Y' ) AS StartDate,
								DATE_FORMAT( EndDate, '%m-%d-%Y' ) AS EndDate,
								/*==*/

								(SELECT  CASE count(*)
								WHEN 0 THEN ''
								ELSE group_concat( WeekDayDesc SEPARATOR ',' )
								END
								FROM SchSchoolAssignmentSelDays f
								where a.Id = f.AssignmentId) as SelectedDaysDesc,

								(SELECT  CASE count(*)
								WHEN 0 THEN ''
								ELSE group_concat( WeekDayId SEPARATOR ',' )
								END
								FROM SchSchoolAssignmentSelDays f
								where a.Id = f.AssignmentId) as SelectedDaysId,

								/*==*/

								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	SchSchoolAssignmentHeader a,
									Users b,
									SchServiceTypes d,
									RegistrantTypes f
								WHERE SchoolId = ?
								AND a.UserId = b.UserId
								AND d.ServiceCategoryId = 0
								AND a.ServiceTypeId = d.Id
								AND d.RegistrantTypeId  = f.Id
								ORDER BY  a.TransDate Desc ";

                        return $this->getAll($query, [$SchoolId], 's');
			}

			// Get School (SCH) Registrant Types Listing
            //================================
			function getSchSchoolRegistrantTypes() {

                        $query = "SELECT Id as id,
										 Id as RegistrantTypeId,
									     RegistrantGroupId ,
                                         RegistrantTypeDesc
									FROM RegistrantTypes
									WHERE RegistrantGroupId = '7'
									ORDER BY RegistrantTypeDesc";

                        return $this->getAll($query);
			}			

			// Get School (SCH) Service Types Listing
            //================================
			function getSchSchoolServiceTypes($ServiceCategoryId) {

                        $query = "SELECT 	a.Id as id,
									        a.Id as ServiceTypeId,
									        a.RegistrantTypeId,
									       CONCAT(ServiceTypeDesc, ' (', RegistrantTypeDesc, ') ') as ServiceTypeDesc
									FROM SchServiceTypes a, RegistrantTypes b
									WHERE ServiceCategoryId = ?
									AND   b.Id  = a.RegistrantTypeId
									ORDER BY a.RegistrantTypeId ";

                        return $this->getAll($query, [$ServiceCategoryId], 's');
			}
 

 			/* Set (SCH) School Assignment Header */
            /*====================================== */
			function setSchSchoolAssignmentHeader(  $SchoolId,
													$StatusId,
													$AssignmentId,
													$AssignmentTypeId,
													$ConfirmationNumber,
													$StartDate,
													$EndDate,
													$ServiceTypeId,
													$UserId )
			{

	            	$query = "CALL proc_setSchSchoolAssignmentHeader (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                       $this->getAll($query, [$SchoolId, $StatusId, $AssignmentId, $AssignmentTypeId, $ConfirmationNumber, $StartDate, $EndDate, $ServiceTypeId, $UserId], 'sssssssss');

						return $query;
			}

			// Get (SCH) School Assignment Details 
            //=========================================			
		 	

		 	function getSchSchoolAssignmentDetails($AssignmentId) {

                        $query ="SELECT a.Id,
                        				a.Id AS id,
                        				b.WeekDayId,
                        				b.WeekDay,
                        				CASE
											WHEN a.StartTime THEN a.StartTime
											ELSE '07:00:00'
										END AS StartTime,

                        				CASE
											WHEN a.EndTime THEN a.EndTime
											ELSE '15:00:00'
										END AS EndTime,


                        				CASE
											WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' )
											ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )
										END AS StartTimeFrm,

                        				CASE
											WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' )
											ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
										END AS EndTimeFrm,



                        				CASE
											WHEN a.TotalHours THEN a.TotalHours
											ELSE '8.00'
										END AS TotalHours,
										a.RegistrantId,
										COALESCE(( SELECT CONCAT( TRIM( c.LastName) , ', ', TRIM( c.FirstName) ,' (', RegistrantTypeDesc,')' )
															FROM 	Registrants c, RegistrantTypes f
													WHERE a.RegistrantId = c.Id
													AND   c.TypeId = f.Id ),'Not Selected') as RegistrantName,
								COALESCE(CONCAT( TRIM( c.FirstName ) , ' ', TRIM( c.LastName ) ),'') AS UserName,
								COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                        FROM  SchSchoolAssignmentDetails a

						RIGHT JOIN DaysOfWeek b
						ON a.WeekDayId = b.WeekDayId
						AND a.AssignmentId = ?


						LEFT JOIN Users c
						ON a.Userid = c.Userid";

                        return $this->getAll($query, [$AssignmentId], 's');
				}
		
			// Get (SCH) Schools Approved Registrants
            //=========================================
		 	function getSchSchoolApprovedRegistrants($ServiceTypeId) {

                        $query ="SELECT distinct a.RegistrantId as id,
                        				a.RegistrantId,
							       		CONCAT( TRIM( c.LastName) , ', ', TRIM( c.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName

							 FROM 	ClientApprovedRegistrants a,
							        Registrants c,
							        RegistrantTypes f,
							        Clients b,
							        SchServiceTypes d
							WHERE a.ClientId = b.Id
							AND   a.RegistrantId = c.Id
							AND   b.SchoolFL = '1'
							AND   c.TypeId = f.Id
							AND   d.Id = ?
							AND   FIND_IN_SET(c.TypeId, d.RegistrantTypeIdMult )
							ORDER BY c.LastName, c.FirstName";

                        return $this->getAll($query, [$ServiceTypeId], 's');
			}				


			/* Set School (SCH) Assignment Detail  */
            /*========================================== */
			function setSchSchoolAssignmentDetail(  $AssignmentId,
													$AssignmentDetailId,
													$WeekDayId,
													$StartTime,
													$EndTime,
													$TotalHours,
													$RegistrantId,
													$UserId )
			{

				if(is_numeric($AssignmentDetailId) ) {

                        $query ="UPDATE SchSchoolAssignmentDetails
								SET AssignmentId = ?,
								StartTime = ?,
								EndTime = ?,
								TotalHours = ?,
								RegistrantId = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

                        return $this->execute($query, [$AssignmentId, $StartTime, $EndTime, $TotalHours, $RegistrantId, $UserId, $AssignmentDetailId], 'sssssss');
                } else {
                       $query ="INSERT INTO SchSchoolAssignmentDetails
								(AssignmentId,
								WeekDayId,
								StartTime,
								EndTime,
								TotalHours,
								RegistrantId,
								UserId,
								TransDate )
				VALUES 	(?, ?, ?, ?, ?, ?, ?, NOW())";

                        return $this->execute($query, [$AssignmentId, $WeekDayId, $StartTime, $EndTime, $TotalHours, $RegistrantId, $UserId], 'sssssss');
				}
			}


			/* Set School (SCH) Assignment Detail - ALL */
            /*========================================== */
			function setSchSchoolAssignmentDetailAll(  $AssignmentId,
														$StartTime,
														$EndTime,
														$TotalHours,
														$RegistrantId,
														$UserId )
			{

				$query ="DELETE FROM SchSchoolAssignmentDetails
							WHERE AssignmentId = ?";

				$this->execute($query, [$AssignmentId], 's');

                sleep(1);

				$query ="INSERT INTO SchSchoolAssignmentDetails
												(AssignmentId,
												WeekDayId,
												StartTime,
												EndTime,
												TotalHours,
												RegistrantId,
												UserId,
												TransDate )
					SELECT  ?,
						    WeekDayId,
							?,
							?,
							?,
							?,
							?,
							NOW()
					FROM     DaysOfWeek";

                return $this->execute($query, [$AssignmentId, $StartTime, $EndTime, $TotalHours, $RegistrantId, $UserId], 'ssssss');
			}

 			/* Set (SCH) School Weekly Scheudle */
            /*====================================== */
			function getSchSchoolWklySchedules( $SchoolId,
												$PayrollWeek )
			{

	            	$query = "CALL proc_getSchSchoolWeeeklySchedules (?, ?)";

                return $this->getAll($query, [$SchoolId, $PayrollWeek], 'ss');
			}

			// Set (SCH) School Weekly Service(s) from Template
            //============================================
			function setSchSchoolWklyServicesFromTemplate(	$PayrollWeek,
															$SchoolId,
															$ServiceTypeId,
															$ConfirmationNumber,
															$ServiceDate,
															$StartTime,
															$EndTime,
															$TotalHours,
															$WeekDay,
															$UserId )
			{
					$query ="	INSERT INTO WeeklyServices
		                (
									ClientId,
									PayrollWeek,
									SchoolId,
									ScheduleStatusId,
									ServiceTypeId,
									AssignmentTypeId,
									ConfirmationNumber,
									ServiceDate,
									StartTime,
									EndTime,
									TotalHours ,
									WeekDay ,
									UserId,
									TransDate
								)

							SELECT  a.Id,
									?,
									?,
									'0',
									?,
									'0',
									?,
									?,
									?,
									?,
									?,
									?,
									?,
									NOW()
							FROM  Clients a
							WHERE SchoolFL = '1' LIMIT 1";

					$this->execute($query, [$PayrollWeek, $SchoolId, $ServiceTypeId, $ConfirmationNumber, $ServiceDate, $StartTime, $EndTime, $TotalHours, $WeekDay, $UserId], 'ssssssssss');

				return $query;
			}

			// Check Registrants Duplicate Assigmentd
            //=========================================
		 	function getRegistrantDuplicateScheduleFL($RegistrantId, $ServiceDate,  $StartTime) {

                        $query ="SELECT count(*) as AlredyExistsFL
									FROM WeeklyServices
									WHERE RegistrantId = ?
									AND ServiceDate = ?
									AND (? BETWEEN StartTime AND EndTime)";

                        return $this->getAll($query, [$RegistrantId, $ServiceDate, $StartTime], 'sss');
			}				


 	        //===============================================
			// Set (SCH) Adjust Client Schedule
            //===============================================
			function setSchSchoolWklyScheduleAdjust (
								$ScheduleId,
								$StartTime,
								$EndTime,
								$TotalHours,
								$ScheduleStatusId,
								$ConfirmationNumber,
								$RegistrantId,
								$UserId)
			{
                        $query = "UPDATE WeeklyServices
						            SET StartTime = ?,
                                        EndTime = ?,
										TotalHours = ?,
										ScheduleStatusId = ?,
										ConfirmationNumber = ?,
										RegistrantId = ?,
                                        UserId = ?,
                                        TransDate = NOW()
									WHERE Id = ?";

						$this->execute($query, [$StartTime, $EndTime, $TotalHours, $ScheduleStatusId, $ConfirmationNumber, $RegistrantId, $UserId, $ScheduleId], 'ssssssss');

						return $query;
			}

			// Get School (SCH) Side Navigation Data
            //==============================================
			function getSchStudentSideNavigation() {

                        $query = "SELECT *
						FROM SchStudentSideNavigation
						ORDER BY id";

                        return $this->getAll($query);
			}

			/* Get Students (SCH) Listing
            //======================= */
			function getSchStudents($Statuses) {
                        $query = "SELECT Id as id,
										/*CONCAT( TRIM( LastName) , ', ', TRIM(FirstName) ) as StudentName,*/
										CONCAT( TRIM( LastName) , ', ', TRIM(FirstName) ,' (', ExtId,')' ) as StudentName,
										SearchId
							FROM SchStudents
							WHERE StatusID IN {$Statuses}
							ORDER BY LastName, FirstName";

                        return $this->getAll($query);
			}

			/* Get Selected (SCH) Student's General Info */
            /*======================= */
			function getSelSchStudent($StudentId) {

                        $query ="SELECT  Id as id,
					                    Id,
										ExtId,
										SearchId,
										COALESCE(SchoolId,'') as SchoolId,
										StatusId,
										DateOfBirth,
										FirstName,
										LastName,
										CONCAT( TRIM( LastName) , ', ', TRIM(FirstName)) as StudentName,
										MiddleInitial,
										StreetAddress1,
										StreetAddress2,
										City ,
										State ,
										ZipCode ,
										COALESCE(MobilePhone,'') as MobilePhone,
										COALESCE(HomePhone,'') as HomePhone,
										GuardianName,
										COALESCE(GuardianPhone,'') as GuardianPhone,
										GuardianEmail,
										MedicalNeeds,
										Comments,
										ReportGroupId,
										UserId

									FROM SchStudents
								  WHERE Id = ?";

                        return $this->getAll($query, [$StudentId], 's');
			}

			/* Update Selected (SCJ) Student's Info
            //======================= */
			function setSelSchStudent( 	$Id,
										$ExtId,
										$SearchId,
										$StatusId,
										$SchoolId,
										$DateOfBirth,
										$FirstName,
										$LastName,
										$MiddleInitial,
										$StreetAddress1,
										$StreetAddress2,
										$City,
										$State,
										$ZipCode,
										$MobilePhone,
										$HomePhone,
										$GuardianFirstName,
										$GuardianLastName,
										$GuardianPhone,
										$GuardianEmail,
										$MedicalNeeds,
										$Comments,
										$ReportGroupId,
										$UserId	)
				{
                if(is_numeric($Id) ) {

                        $query ="UPDATE SchStudents
								SET ExtId = ?,
								SearchId = ?,
								StatusId = ?,
								SchoolId = ?,
								DateOfBirth = ?,
								FirstName = ?,
								LastName = ?,
								MiddleInitial = ?,
								StreetAddress1 = ?,
								StreetAddress2 = ?,
								City = ?,
								State = ?,
								ZipCode = ?,
								MobilePhone = ?,
								HomePhone = ?,
								GuardianFirstName = ?,
								GuardianLastName = ?,
								GuardianPhone = ?,
								GuardianEmail = ?,
								MedicalNeeds = ?,
								Comments = ?,
								ReportGroupId = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

						$this->execute($query, [$ExtId, $SearchId, $StatusId, $SchoolId, $DateOfBirth, $FirstName, $LastName, $MiddleInitial, $StreetAddress1, $StreetAddress2, $City, $State, $ZipCode, $MobilePhone, $HomePhone, $GuardianFirstName, $GuardianLastName, $GuardianPhone, $GuardianEmail, $MedicalNeeds, $Comments, $ReportGroupId, $UserId, $Id], 'ssssssssssssssssssssssss');
						return $query;
                } else {
                       $query ="INSERT INTO SchStudents
								(ExtId,
								SearchId,
								StatusId,
								SchoolId,
								DateOfBirth,
								FirstName,
								LastName,
								MiddleInitial,
								StreetAddress1,
								StreetAddress2,
								City,
								State,
								ZipCode,
								MobilePhone,
								HomePhone,
								GuardianFirstName,
								GuardianLastName,
								GuardianPhone,
								GuardianEmail,
								MedicalNeeds,
								Comments,
								ReportGroupId,
								UserId,
								TransDate )
				VALUES 	(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

						$this->execute($query, [$ExtId, $SearchId, $StatusId, $SchoolId, $DateOfBirth, $FirstName, $LastName, $MiddleInitial, $StreetAddress1, $StreetAddress2, $City, $State, $ZipCode, $MobilePhone, $HomePhone, $GuardianFirstName, $GuardianLastName, $GuardianPhone, $GuardianEmail, $MedicalNeeds, $Comments, $ReportGroupId, $UserId], 'sssssssssssssssssssssss');
						return $query;
                }
			}					

			// Get (SCH) Student Messages
            //=========================================
		 	function getSchStudentMessages($StudentId) {

                        $query ="SELECT Id as MsgId ,
								Msg,
								HighPriority,

								CASE HighPriority
									WHEN '1' THEN 'High'
										ELSE 'Normal'
								END AS HighPriorityLast,
								CASE HighPriority
									WHEN '1' THEN 'red'
										ELSE 'black'
								END AS PriorityColor,

								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM SchStudentMessages a, Users b
								WHERE StudentId = ?
								AND a.UserId = b.UserId
								ORDER BY a.TransDate DESC";

                        return $this->getAll($query, [$StudentId], 's');
			}				

			/* Set (SCH) Student Message */
            /*======================= */
			function setSchStudentMessages  ($StudentId,
											$HighPriority,
											$Msg,
											$UserId )
			{

                       $query ="INSERT INTO SchStudentMessages
								(StudentId,
								HighPriority,
								Msg,
								UserId,
								TransDate )
				VALUES 	(?, ?, ?, ?, NOW())";

                        return $this->execute($query, [$StudentId, $HighPriority, $Msg, $UserId], 'ssss');
			}

			// Get (SCH) Student Assignment Headers
            //=========================================
		 	function getSchStudentAssignmentHeaders($StudentId) {

                        $query ="SELECT a.Id as Id ,
								a.Id,
								a.StudentId,
								a.StatusId,
								ServiceTypeId as OrigServiceTypeId,
								ServiceTypeId,
								ServiceTypeDesc,
								RegistrantTypeId,
                                RegistrantTypeDesc,
								AssignmentTypeId,
								CASE AssignmentTypeId
									WHEN '1' THEN 'Long Term'
										ELSE 'Per Diem'
								END AS AssignmentTypeDesc,
								ConfirmationNumber,
								DATE_FORMAT( StartDate, '%m-%d-%Y' ) AS StartDate,
								DATE_FORMAT( EndDate, '%m-%d-%Y' ) AS EndDate,
								/*==*/

								(SELECT  CASE COUNT(*)
								WHEN 0 THEN ''
								ELSE GROUP_CONCAT( WeekDayDesc SEPARATOR ',' )
								END
								FROM SchSchoolAssignmentSelDays f
								WHERE a.Id = f.AssignmentId) as SelectedDaysDesc,

								(SELECT  CASE COUNT(*)
								WHEN 0 THEN ''
								ELSE GROUP_CONCAT( WeekDayId SEPARATOR ',' )
								END
								FROM SchSchoolAssignmentSelDays f
								WHERE a.Id = f.AssignmentId) as SelectedDaysId,

								/*==*/

								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ) as TransDate
							FROM 	SchStudentAssignmentHeader a,
									Users b,
									SchServiceTypes d,
									RegistrantTypes f
								WHERE StudentId = ?
								AND a.UserId = b.UserId
								AND d.ServiceCategoryId = 1 /* Category: Student */
								AND a.ServiceTypeId = d.Id
								AND d.RegistrantTypeId  = f.Id
								ORDER BY a.TransDate DESC";

                        return $this->getAll($query, [$StudentId], 's');
			}				


			/* Set (SCH) Student Assignment Header */
            /*====================================== */
			function setSchStudentAssignmentHeader( $StudentId,
													$StatusId,
													$AssignmentId,
													$AssignmentTypeId,
													$ConfirmationNumber,
													$StartDate,
													$EndDate,
													$ServiceTypeId,
													$UserId )
			{


	            	$query = "CALL proc_setSchStudentAssignmentHeader (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    $this->getAll($query, [$StudentId, $StatusId, $AssignmentId, $AssignmentTypeId, $ConfirmationNumber, $StartDate, $EndDate, $ServiceTypeId, $UserId], 'sssssssss');
					return $query;
			}


			// Get (SCH) Student Assignment Details 
            //=========================================			
		 	

		 	function getSchStudentAssignmentDetails($AssignmentId) {

                        $query ="SELECT a.Id,
                        				a.Id AS id,
                        				b.WeekDayId,
                        				b.WeekDay,
                        				CASE
											WHEN a.StartTime THEN a.StartTime
											ELSE '07:00:00'
										END AS StartTime,

                        				CASE
											WHEN a.EndTime THEN a.EndTime
											ELSE '15:00:00'
										END AS EndTime,


                        				CASE
											WHEN a.StartTime THEN DATE_FORMAT( a.StartTime, '%l:%i %p' )
											ELSE DATE_FORMAT( '1900-01-01 07:00:00', '%l:%i %p' )
										END AS StartTimeFrm,

                        				CASE
											WHEN a.EndTime THEN DATE_FORMAT( a.EndTime, '%l:%i %p' )
											ELSE DATE_FORMAT( '1900-01-01 15:00:00', '%l:%i %p' )
										END AS EndTimeFrm,



                        				CASE
											WHEN a.TotalHours THEN a.TotalHours
											ELSE '8.00'
										END AS TotalHours,
										a.RegistrantId,
										COALESCE(( SELECT CONCAT( TRIM( c.LastName) , ', ', TRIM( c.FirstName) ,' (', RegistrantTypeDesc,')' )
															FROM 	Registrants c, RegistrantTypes f
													WHERE a.RegistrantId = c.Id
													AND   c.TypeId = f.Id ),'Not Selected') as RegistrantName,
								COALESCE(CONCAT( TRIM( c.FirstName ) , ' ', TRIM( c.LastName ) ),'') AS UserName,
								COALESCE(DATE_FORMAT( a.TransDate, '%m-%d-%Y %r' ),'') as TransDate


                        FROM  SchStudentAssignmentDetails a

						RIGHT JOIN DaysOfWeek b
						ON a.WeekDayId = b.WeekDayId
						AND a.AssignmentId = ?


						LEFT JOIN Users c
						ON a.Userid = c.Userid";

                        return $this->getAll($query, [$AssignmentId], 's');

				}		

			/* Set Student (SCH) Assignment Detail  */
            /*========================================== */
			function setSchStudentAssignmentDetail( $AssignmentId,
													$AssignmentDetailId,
													$WeekDayId,
													$StartTime,
													$EndTime,
													$TotalHours,
													$RegistrantId,
													$UserId )
			{


				if(is_numeric($AssignmentDetailId) ) {

                        $query ="UPDATE SchStudentAssignmentDetails
								SET AssignmentId = ?,
								StartTime = ?,
								EndTime = ?,
								TotalHours = ?,
								RegistrantId = ?,
								UserId = ?,
								TransDate = NOW()
							WHERE Id = ?";

						return $this->execute($query, [$AssignmentId, $StartTime, $EndTime, $TotalHours, $RegistrantId, $UserId, $AssignmentDetailId], 'sssssss');
                } else {
                       $query ="INSERT INTO SchStudentAssignmentDetails
								(AssignmentId,
								WeekDayId,
								StartTime,
								EndTime,
								TotalHours,
								RegistrantId,
								UserId,
								TransDate )
				VALUES 	(?, ?, ?, ?, ?, ?, ?, NOW())";

						return $this->execute($query, [$AssignmentId, $WeekDayId, $StartTime, $EndTime, $TotalHours, $RegistrantId, $UserId], 'sssssss');
				}
			}


			/* Set Student (SCH) Assignment Detail - ALL */
            /*========================================== */
			function setSchStudentAssignmentDetailAll(  $AssignmentId,
														$StartTime,
														$EndTime,
														$TotalHours,
														$RegistrantId,
														$UserId )
			{

				$query ="DELETE FROM SchStudentAssignmentDetails
							WHERE AssignmentId = ?";

				$this->execute($query, [$AssignmentId], 's');


                sleep(1);


				$query ="INSERT INTO SchStudentAssignmentDetails
												(AssignmentId,
												WeekDayId,
												StartTime,
												EndTime,
												TotalHours,
												RegistrantId,
												UserId,
												TransDate )
					SELECT  ?,
						    WeekDayId,
							?,
							?,
							?,
							?,
							?,
							NOW()
					FROM     DaysOfWeek";

                return $this->execute($query, [$AssignmentId, $StartTime, $EndTime, $TotalHours, $RegistrantId, $UserId], 'ssssss');
			}

 			/* Set (SCH) Student Weekly Scheudle */
            /*====================================== */
			function getSchStudentWklySchedules( $StudentId,
												 $PayrollWeek )
			{


	            	$query = "CALL proc_getSchStudentWeeeklySchedules (?, ?)";

                    return $this->getAll($query, [$StudentId, $PayrollWeek], 'ss');
			}


			// Set (SCH) Student Weekly Service(s) from Template
            //============================================
			function setSchStudentWklyServicesFromTemplate(	$PayrollWeek,
															$StudentId,
															$ServiceTypeId,
															$ConfirmationNumber,
															$ServiceDate,
															$StartTime,
															$EndTime,
															$TotalHours,
															$WeekDay,
															$UserId )
			{
					$query ="INSERT INTO WeeklyServices
		                (
									ClientId,
									PayrollWeek,
									StudentId,
									ScheduleStatusId,
									ServiceTypeId,
									AssignmentTypeId,
									ConfirmationNumber,
									ServiceDate,
									StartTime,
									EndTime,
									TotalHours,
									WeekDay,
									UserId,
									TransDate
								)

							SELECT  a.Id,
									?,
									?,
									'0',
									?,
									'0',
									?,
									?,
									?,
									?,
									?,
									?,
									?,
									NOW()
							FROM  Clients a
							WHERE SchoolFL = '1' LIMIT 1";

				$this->execute($query, [$PayrollWeek, $StudentId, $ServiceTypeId, $ConfirmationNumber, $ServiceDate, $StartTime, $EndTime, $TotalHours, $WeekDay, $UserId], 'ssssssssss');
				return $query;
			}

			//====================================
			// Get (SCH) Student Monthly Calendar
			//====================================
			function getSchStudentMonthlyCalendar($StudentId, $StartDate) {

                    $query = "CALL proc_getSchStudentMontlyCalendar (?, ?)";
                    return $this->getAll($query, [$StudentId, $StartDate], 'ss');
			}

			//====================================
			// Get (SCH) School Monthly Calendar
			//====================================
			function getSchSchoolMonthlyCalendar($SchoolId, $StartDate) {

                    $query = "CALL proc_getSchSchoolMontlyCalendar (?, ?)";
                    return $this->getAll($query, [$SchoolId, $StartDate], 'ss');
			}

			// Get (SCH) Schoolboard Side Navigation Data
            //=======================
			function getSchSchoolboardSideNavigation() {

                        $query = "SELECT *
						FROM SchSchoolboardNavigation
					    WHERE Status = 1
						ORDER BY id";

                        return $this->getAll($query);
			}


			// Get (SCH) Student Unassigned Mandates Data
            //======================================
			function getSchStudentUnassignedMandates() {

                        $query = "SELECT 	a.Id as MandateId,
												a.Id,
												StudentId,
												b.SearchId as StudentSearchId,
												CONCAT( TRIM( b.LastName) , ', ', TRIM(b.FirstName)) as StudentName,
												a.SchoolId,

												CASE a.SchoolId
												WHEN '0' THEN 'School Undefined'
												ELSE (SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
															FROM    SchSchools c,
																	SchDistricts d
																	WHERE a.SchoolId = c.Id
																	AND   c.DistrictId = d.Id
															)
												END AS SchoolName,

												CASE a.RegistrantId
												WHEN '0' THEN 'Registrant Undefined'
												ELSE (SELECT CONCAT( TRIM( d.LastName ) , ', ',TRIM( d.FirstName))
		                         					FROM  Registrants d
													WHERE  a.RegistrantId = d.Id

												)
												END AS RegistrantName,

												ServiceTypeDesc,
												CONCAT( `SessionFrequency` , ' X ', `SessionLength` , ' X ', `SessionGrpSize` ) AS MandateDesc,
												Language,
												'Registrant Not Assigned ' AS ProblemDesc,
												CONCAT( TRIM( e.FirstName ) , ' ', TRIM( e.LastName ) ) AS UserName,
												a.UserId,
												a.TransDate

										FROM 	SchStudentMandates a,
												SchStudents b,
												SchServiceTypes f,
												Users e
										WHERE a.StatusId = 1
										AND a.StudentId = b.Id
										AND a.ServiceTypeId = f.Id
										AND a.UserId = e.UserId
										AND ((a.RegistrantId = 0) OR (a.SchoolId = 0))
										ORDER BY b.LastName, b.FirstName";

                        return $this->getAll($query);
			}

			// Get (SCH) Student Mandates List Data
            //======================================
			function getSchStudentMandatesList($StudentId) {

	                        $query = "SELECT 	a.Id as id,
												a.Id as MandateId,
												StudentId,
												CONCAT( TRIM( b.LastName) , ', ', TRIM(b.FirstName)) as StudentName,
												a.StatusId,
												CASE a.SchoolId
													WHEN '0' THEN ''
												ELSE a.SchoolId
												END AS SchoolId,

												(SELECT SchoolName FROM SchSchools c
												   WHERE a.SchoolId = c.Id ) as SchoolName,
												ServiceTypeDesc,
												ServiceTypeId,
												a.StatusId,
												SECMandateStatus,
												DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
												DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,
												CASE a.RegistrantId
													WHEN '0' THEN ''
												ELSE a.RegistrantId
												END AS RegistrantId,

												CallInDate,
												DOEAssignmentDate,
												PlaceOfService,

												SECMandateStatus,
												DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
												CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize ) AS MandateDesc,
												Language,
												CONCAT( TRIM( RegistrantExtFirstName) , ' ', TRIM(RegistrantExtLastName)) as RegistrantName,
												CONCAT( TRIM( e.FirstName ) , ' ', TRIM( e.LastName ) ) AS UserName,
												DOESchoolName,
												a.UserId,
												a.TransDate

										FROM 	SchStudentMandates a,
												SchStudents b,
												SchServiceTypes f,
											Users e
											WHERE a.StudentId = ?
											AND a.StudentId = b.Id
											AND a.ServiceTypeId = f.Id
											AND a.UserId = e.UserId
										ORDER BY ServiceTypeId";

                        return $this->getAll($query, [$StudentId], 's');
			}

			// Get (SCH) Student Mandate Data
            //======================================
			function getSchStudentMandate($MandateId) {

                        $query = "SELECT 	a.Id as id,
											a.Id,
											StudentId,
											a.StatusId,
											SECMandateStatus,
											DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
											DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,
											RegistrantId,
											CallInDate,
											DOEAssignmentDate,
											PlaceOfService,
											CONCAT( TRIM( f.FirstName ) , ' ', TRIM( f.LastName ) ) AS UserName,
											a.UserId,
											a.TransDate
						FROM SchStudentMandates a,
							 SchServiceTypes e,
							 Users f
						WHERE a.Id = ?
						AND a.ServiceTypeId = e.Id
						AND a.UserId = f.UserId";

                        return $this->getAll($query, [$MandateId], 's');
			}

			// Set (SCH) Student Mandate Data
            //======================================
			function setSchStudentMandate(	$MandateId,
											$StatusId,
											$PlaceOfService,
											$StartDate,
											$EndDate,
											$SchoolId,
											$RegistrantId,
											$UserId ) {

                        $query = "UPDATE SchStudentMandates
								SET StatusId = ?,
								    PlaceOfService = ?,
									StartDate = ?,
									EndDate = ?,
									SchoolId = ?,
									RegistrantId = ?,
								    UserId = ?,
									TransDate = NOW()
							WHERE 	Id = ?";

                        return $this->execute($query, [$StatusId, $PlaceOfService, $StartDate, $EndDate, $SchoolId, $RegistrantId, $UserId, $MandateId], 'ssssssss');
			}


			// Set (SCH) Student School from Mandate
            //======================================
			function setSchStudentSchoolFromMandate($MandateId,
													$SchoolId,
													$UserId )
			{

                        $query = "UPDATE SchStudents a, SchStudentMandates b
								SET a.SchoolId = ?,
								    a.UserId = ?,
									a.TransDate = NOW()
							WHERE 	b.Id = ?
							AND     a.Id = b.StudentId";

                        return $this->execute($query, [$SchoolId, $UserId, $MandateId], 'sss');
			}


			// Get (SCH) Registrant  Side Navigation Data
            //==============================================
			function getSchRegistrantSideNavigation() {

                        $query = "SELECT *
						FROM SchRegistrantSideNavigation
						ORDER BY id";

                        return $this->getAll($query);
			}

			// Get Registrant Web Navigation Data
            //=======================
			function getSchRegistrantWebNavigation() {

                        $query = "SELECT *
						FROM SchRegistrantWebNavigation
					    WHERE Status = 1
						ORDER BY id";

                        return $this->getAll($query);
			}



			// Get (SCH) Registrants  Listing
            //=======================
			function getSchRegistrants($Statuses, $SearchId) {

                        $query = "SELECT a.Id as id,
											a.TypeId,
											a.ExtId,
											a.SearchId,
											CONCAT( TRIM( a.LastName) , ', ', TRIM(a.FirstName) ,' (', RegistrantTypeDesc,')' ) as RegistrantName
										FROM Registrants a, RegistrantTypes b
										WHERE Typeid  = b.Id
										AND StatusID IN {$Statuses}
										AND SearchId LIKE ?

										AND EXISTS (SELECT 1 FROM Clients c, ClientApprovedRegistrants d
														WHERE a.Id = d.RegistrantId
														AND   d.ClientId = c.Id
														AND   c.SchoolFL = '1'
											)
										ORDER BY LastName, FirstName";

						return $this->getAll($query, [$SearchId], 's');
			}

			// Get (SCH) Registrant Types Listing
            //=======================
			function getSchRegistrantTypes() {

                        $query = "SELECT Id as id,
										 Id as RegistrantTypeId,
									     RegistrantGroupId,
                                         RegistrantTypeDesc
									FROM RegistrantTypes
									WHERE RegistrantStatusId = '1'
									AND RegistrantGroupId = '7'
									ORDER BY RegistrantTypeDesc";

						return $this->getAll($query);
			}

			/* Get (SCH) Registrant's Date Sessions Data
            //===========================*/
			function getSchRegistrantDateSessions($RegistrantId, $ServiceDate) {

                        $query = "SELECT 	Id as id,
											Id as SessionId,
											a.Status,
											RegistrantId,
											ServiceDate,
											DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
											StartTime as StartTimeSort,
											DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
											EndTime as EndTimeSort,
											TotalHours,
											GroupSize,
											AttachedServicesNum,
											(SELECT COUNT(*) FROM SchSchoolWeeklyServices c
											  WHERE c.SessionId = a.Id
											  AND c.SessionId != 0) as NumOfServAttached,
											CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
											TransDate
									FROM SchRegistrantDateSessions a, Users b
										WHERE ServiceDate = ?
										AND RegistrantId = ?
										AND a.UserId = b.UserId
										AND a.Status < 2
									ORDER BY StartTimeSort";

                        return $this->getAll($query, [$ServiceDate, $RegistrantId], 'ss');
			}

			/* Get (SCH) Registrant's Date Sessions Data
            //===========================*/		

			/* Get (SCH) Registrant's Date Sessions Data
            //===========================*/		
			function getSchRegistrantVerifiedDailySessions($RegistrantId, $ServiceDate) {

                        $query = "SELECT  	DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
									 		DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
											FORMAT((a.TotalHours * 60), 0) as TotalHours,
											GROUP_CONCAT( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
											GROUP_CONCAT( a.Id    SEPARATOR ',' ) as SessionSchedulesList,

											SessionGrpSize,
											a.RegistrantId,
											COALESCE((SELECT StoredName FROM SchStudentsSessionNotes h
											WHERE a.Id = h.ScheduleId),'') as StoredDocName,

											COALESCE((SELECT DocumentName FROM SchStudentsSessionNotes h
											WHERE a.Id = h.ScheduleId),'') as DocumentName,
											SessionDeliveryModeId,
											CASE SessionDeliveryModeId
												WHEN 'V' THEN 'Audio & Video'
												WHEN 'A' THEN 'Audio Only'
												WHEN 'I' THEN 'In-Person'
											ELSE 'In-Person'
											END AS SessionDeliveryModeDesc


									FROM 	WeeklyServices a, SchStudents b
												WHERE a.RegistrantId = ?
												AND   a.ServiceDate = ?
												AND   a.ScheduleStatusId > 5
												AND   b.Id = a.StudentId
									GROUP BY a.StartTime, a.EndTime, a.TotalHours";

                        return $this->getAll($query, [$RegistrantId, $ServiceDate], 'ss');
			}


           // Set (SCH) Registrant Verified Mandated Sessions
            //=======================
			function setSchRegistrantVerifiedMandatedSessions(
									$ServiceDate,
									$PayrollWeek,
									$WeekDay,
									$MandateId,
									$StartTime,
									$EndTime,
									$TotalHours,
									$GroupSize,
									$SessionDeliveryModeId,
									$UserId )
					{

							$query ="INSERT INTO WeeklyServices
							                  (
												PayrollWeek,
												ClientId,
												ClientUnitId,
												ScheduleStatusId,
												ServiceDate,
												StartTime,
												EndTime,
												TotalHours,
												WeekDay,
												RegistrantId,
												SchoolId,
												StudentId,
												ServiceTypeId,
												MandateId,
												SessionGrpSize,
												SessionDeliveryModeId,
												UserId,
												TransDate )

						    SELECT
										?,
										a.Id,
										b.Id,
										'7',
										?,
										?,
                                        ?,
                                        ?,
										?,
                                        c.RegistrantId,
										c.SchoolId,
										c.StudentId,
									  	c.ServiceTypeId,
									  	?,
									 	?,
									 	?,
 									 	?,
                                         NOW()
                            FROM Clients a, ClientUnits b, SchStudentMandates c
                            WHERE a.SchoolFL = '1'
                            AND   b.ClientId = a.Id
                            AND   c.Id = ? LIMIT 1";

					$this->execute($query, [$PayrollWeek, $ServiceDate, $StartTime, $EndTime, $TotalHours, $WeekDay, $MandateId, $GroupSize, $SessionDeliveryModeId, $UserId, $MandateId], 'sssssssssss');

				//return $result;
				return $query;
			}

			/* Set (SCH) Registrant Weekly Scheudle */
            /*====================================== */
			function getSchRegistrantWklySchedules( $RegistrantId,
													$PayrollWeek )
			{


	            	$query = "CALL proc_getSchRegistrantWeeeklySchedules (?, ?)";

                       return $this->getAll($query, [$RegistrantId, $PayrollWeek], 'ss');
			}


			/* Get (SCH) School Alert Messages
            ======================= */
		 	function getSchSchoolAlertMessages() {

                        $query ="SELECT Id as AlertId,
								Msg,
								StatusId,
								0 as Seq,
								'Orig' as AlertType,
								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( a.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b
								WHERE a.UserId = b.UserId
								AND a.SchoolFL = '1'

						UNION
								SELECT c.AlertId,
								c.Msg,
								a.StatusId,
								c.Id as Seq,
								'Resp' as AlertType,
								CONCAT( TRIM( b.FirstName ) , ' ', TRIM( b.LastName ) ) AS UserName,
								a.TransDate as SortTransDate,
								DATE_FORMAT( c.TransDate, '%m-%d-%Y %h:%i %p' ) as TransDate
							FROM AlertMessages a, Users b, AlertMessageResponses c
								WHERE a.Id = c.AlertId
								AND c.UserId = b.UserId
								AND a.SchoolFL = '1'
								ORDER BY StatusId, SortTransDate DESC, AlertId DESC, Seq";

                        return $this->getAll($query);
			}

			/* Get (SCH) Schoolboard Reports Data
            //=====================================*/
			function getSchSchoolboardReports() {

                        $query = "SELECT Id as id,
										 ReportDesc,
										 ReportLink
						FROM SchSchoolBoardReports
					ORDER BY Id";

                        return $this->getAll($query);
			}

 			/* Set (SCH) School Date Range Scheudle */
            /*====================================== */
			function getSchSchoolDateRangeSchedules($SchoolId,
													$FromDate,
													$ToDate
													)
			{


	            	$query = "CALL proc_getSchSchoolDateRangeSchedules (?, ?, ?)";

                       return $this->getAll($query, [$SchoolId, $FromDate, $ToDate], 'sss');
			}


 			/* Set (SCH) Student Date Range Scheudle */
            /*====================================== */
			function getSchStudentDateRangeSchedules($StudentId,
												  	 $FromDate,
													 $ToDate
													)
			{


	            	$query = "CALL proc_getSchStudentDateRangeSchedules (?, ?, ?)";

                       return $this->getAll($query, [$StudentId, $FromDate, $ToDate], 'sss');
			}

	 			/* Set (SCH) Registrant Date Range Scheudle */
            /*====================================== */
			function getSchRegistrantDateRangeSchedules($RegistrantId,
												  	 	$FromDate,
													 	$ToDate
													   )
			{


	            	$query = "CALL proc_getSchRegistrantDateRangeSchedules (?, ?, ?)";

                       return $this->getAll($query, [$RegistrantId, $FromDate, $ToDate], 'sss');
			}

			//====================================
			// Get (SCH) Registrant Monthly Calendar
			//====================================
			function getSchRegistrantMonthlyCalendar($RegistrantId, $StartDate) {



                    $query = "CALL proc_getSchRegistrantMontlyCalendar (?, ?)";
                    return $this->getAll($query, [$RegistrantId, $StartDate], 'ss');
			}


 			/* Get (SCH) Schoolboard (Schools) Date Range Scheudle */
            /*====================================== */
			function getSchSchoolboardSchoolsDateRangeSchedules($FromDate,
																$ToDate
																)
			{


	            	$query = "CALL proc_getSchSchoolboardSchoolsDateRangeSchedules (?, ?)";

                       return $this->getAll($query, [$FromDate, $ToDate], 'ss');
			}

			/* Get (SCH) Schoolboard (Students) Date Range Scheudle */
            /*====================================== */
			function getSchSchoolboardStudentsDateRangeSchedules($FromDate,
																$ToDate
																)
			{


	            	$query = "CALL proc_getSchSchoolboardStudentsDateRangeSchedules (?, ?)";

                       return $this->getAll($query, [$FromDate, $ToDate], 'ss');
			}


			/* Get (SCH) Registrant's Assigned Mandates Data
            //===========================================================================*/
			function getSchRegistrantAssignedMandates($RegistrantId, $FromDate, $ToDate) {

                        $query = "SELECT  	a.Id as MandateId,
											a.ServiceTypeId,
										   	c.ServiceTypeDesc,
									       	a.SessionLength,
									       	a.SessionGrpSize,
									       	a.SessionFrequency,
									       	a.StudentId,
									       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
									       	(SELECT COUNT(*) FROM WeeklyServices d
									       	  	WHERE d.MandateId = a.Id
									       	  	AND d.ScheduleStatusId > 5
									       	  	AND d.ServiceDate BETWEEN ? AND ?

											) as NumbeOfSessions

									FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
									WHERE a.RegistrantId = ?
									AND a.StatusId = '1'
									AND b.StatusId = '1'
									AND a.StudentId = b.Id
									AND a.ServiceTypeId = c.Id

									AND EXISTS ( SELECT 1 FROM WeeklyServices d
									       	  	WHERE d.MandateId = a.Id
									       	  	AND d.ScheduleStatusId > 5
									       	  	AND d.ServiceDate BETWEEN ? AND ?

									)


                                    ORDER BY b.LastName, b.FirstName";

                        return $this->getAll($query, [$FromDate, $ToDate, $RegistrantId, $FromDate, $ToDate], 'sssss');
			}

			// Set (SCH) Registant Client (NYC DOE) Approval (New Registrants ONLY)
            //====================================================================
			function setSchRegistrantClientApproval($SearchId,
													$TypeId,
													$UserId )
			{

                        $query = " INSERT INTO ClientApprovedRegistrants (
										SearchId,
										Status,
										ClientId,
										ClientUnitId,
										ServiceTypeId,
										RegistrantId,
										UserId,
										TransDate )



							SELECT RAND(),
									'1',
									a.Id,
									(SELECT b.Id
										FROM ClientUnits b
										WHERE b.ClientId = a.Id
										LIMIT 1
									),

									(SELECT c.DefaultServiceTypeId
										FROM RegistrantTypes c
										WHERE c.Id = ?
									),

									f.Id,
									?,

									 NOW()

									FROM Clients a, Registrants f
									WHERE SchoolFL = '1'
									AND f.SearchId = ?";

                        $this->execute($query, [$TypeId, $UserId, $SearchId], 'sss');

						return $query;
			}



			/* Get (SCH) Registrant's Assigned Students Data
            //===========================================================================*/
			function getSchRegistrantAssignedStudents($RegistrantId) {

                        $query = "SELECT  	a.Id as MandateId,
											a.ServiceTypeId,
										   	c.ServiceTypeDesc,
									    /*
									       	a.SessionLength,
									       	a.SessionGrpSize,
									       	a.SessionFrequency,
										*/
											CONCAT( a.SessionFrequency , ' X ', a.SessionLength , ' X ', a.SessionGrpSize ) AS MandateDesc,

									       	a.StudentId,
									       	b.ExtId,

									       	COALESCE(DATE_FORMAT( (COALESCE((SELECT MIN(ServiceDate) FROM WeeklyServices f
									       	    WHERE a.Id = f.MandateId
									       	    AND   a.StudentId = f.StudentId
									       	    AND   f.ScheduleStatusId > 5
									       	    AND   a.RegistrantId = ?
									       	),'')), '%m-%d-%Y' ),'') as ServiceStartDate,
									       	/*
									       	COALESCE((SELECT MIN(ServiceDate) FROM WeeklyServices f
									       	    WHERE a.Id = f.MandateId
									       	    AND   a.RegistrantId = ?
									       	),'') as ServiceStartDate,
									       	*/
									       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
											b.SearchId as StudentSearchId,
											a.SchoolId,

											CASE a.SchoolId
											WHEN '0' THEN 'School Undefined'
											ELSE (SELECT CONCAT(TRIM(SchoolName),' (',DistrictName,') ')
														FROM    SchSchools f,
																SchDistricts d
																WHERE a.SchoolId = f.Id
																AND   f.DistrictId = d.Id
														)
											END AS SchoolName

									FROM SchStudentMandates a, SchStudents b, SchServiceTypes c
									WHERE a.RegistrantId = ?
									AND a.StatusId = '1'
									AND b.StatusId = '1'
									AND a.StudentId = b.Id
									AND a.ServiceTypeId = c.Id


                                    ORDER BY b.LastName, b.FirstName";

                        return $this->getAll($query, [$RegistrantId, $RegistrantId], 'ss');
			}

			// Set Change Schedule Status
            //=======================
			function setChangeScheduleStatus(
								$ScheduleId,
								$ScheduleStatusId,
								$UserId)
			{
                        $query = "UPDATE WeeklyServices
						            SET ScheduleStatusId = ?,
                                        UserId = ?,
                                        TransDate = NOW()
									WHERE  Id = ?";

						$this->execute($query, [$ScheduleStatusId, $UserId, $ScheduleId], 'sss');

						//return $result;
						return $query;
			}

			/* Get (SCH) Registrant's Sesion Notes
            //===========================================================================*/
			function getSchRegistrantSessionNotes($RegistrantId) {

                        $query = "SELECT  a.Id AS Id,
                                a.Id as SessionNotesId,
                                OriginalFileName as SessionNotesDesc,
                            /*    OriginalFileName */
                                StoredName,
          						StoredNameExt,
                                CONCAT( TRIM( e.FirstName ) , ' ', TRIM( e.LastName ) ) as UserName,
                                a.TransDate

                FROM    SchRegistrantSessionNotes a,
                        Users e
                WHERE   a.RegistrantId = ?
                    AND a.UserId = e.UserId";

                        return $this->getAll($query, [$RegistrantId], 's');
			}

			/* Check Student's Duplicate Sessions
            //===========================================================================*/
			function getSchStudentCheckDupSessions($MandateId, $ServiceDate, $StartTime, $EndTime) {

                        $query = "SELECT  COUNT(*) as Dup_Count  FROM WeeklyServices a,  SchStudentMandates b
										WHERE b.Id = ?
										AND   a.ScheduleStatusId > 6
										AND   a.StudentId = b.StudentId
										AND   a.ServiceDate = ?
										AND   a.StartTime BETWEEN ? AND SUBTIME(?, '1')";

                        return $this->getAll($query, [$MandateId, $ServiceDate, $StartTime, $EndTime], 'ssss');
			}

			/* Check Mandates's Remaining Freq.
            //===========================================================================*/		

     		function getSchMandateRemainingFreq($MandateId, $PayrollWeek ) {

                        $query = "SELECT  a.SessionFrequency - (SELECT COUNT(*)
                                  FROM WeeklyServices b
                                  WHERE a.Id = b.MandateId
                                  AND b.ScheduleStatusId > 6
                                  AND b.PayrollWeek = ?
                              ) as RemaininqFreq

               FROM SchStudentMandates a
               WHERE a.Id = ?";

            return $this->getAll($query, [$PayrollWeek, $MandateId], 'ss');
      		}

} // End

?>
