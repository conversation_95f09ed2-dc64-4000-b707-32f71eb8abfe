<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$OfficeEmployeeId = $_GET['OfficeEmployeeId'];
	$WorkDate = $_GET['WorkDate'];


    $query = "   SELECT Id as PayrollRecordId,
              PayrollTypeId,
              case PayrollTypeId 
               when '1' then 'Regular'
               else 'Time Off'
              end as PayrollTypeDesc, 

              Workdate as WorkdateUnf,
              DATE_FORMAT(  WorkDate, '%m-%d-%Y' ) AS WorkDate, 
            DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
            StartTime as StartTimeUnf,
            DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
            EndTime as EndTimeUnf,
            TotalHours  

         from    OfficeEmployeesPayrollRecords  
            WHERE OfficeEmployeeId = '{$OfficeEmployeeId}'  
            AND WorkDate = '{$WorkDate}'
            AND PaidFL = '0'

      "; 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

