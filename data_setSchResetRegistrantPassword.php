<?php

	require_once('DB.php');
	include("db_login.php");
 
    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 

	$NotificationEmails = $_POST['NotificationEmails'];
	$Subject = $_POST['Subject'];
	$Message = $_POST['Message'];
	$RegistrantId = $_POST['RegistrantId'];
	$RegistrantFirstNameInit = $_POST['RegistrantFirstNameInit'];
	$RegistrantLastName = $_POST['RegistrantLastName'];


	$RegistrantFirstNameInit = $connection->escapeSimple($RegistrantFirstNameInit);
	$RegistrantLastName = $connection->escapeSimple($RegistrantLastName);

	$RegistrantLastName = trim($RegistrantLastName);	

    $login = $RegistrantFirstNameInit.$RegistrantLastName;
    $pass = $login.'1';  

    $query = "UPDATE Users b, Registrants a 
				set b.Login = '{$login}', 
				    b.Password = MD5('{$pass}'),
				    b.ResetFL = '1'
				WHERE a.Id = '{$RegistrantId}'
				AND b.ExtId =  a.SearchId";
	
	$result = $connection->getAll($query, DB_FETCHMODE_ASSOC);

  	echo $query;	


	/*==================*/

 

	$to      = $NotificationEmails;
	
	$subject = $Subject;
	$message = $Message;
	$headers = 'From: <EMAIL>' . "\r\n" .
	    'Reply-To: <EMAIL>' . "\r\n" .
	    'X-Mailer: PHP/' . phpversion();

	mail($to, $subject, $message, $headers);
 
	 
	$connection->disconnect();
 

?>