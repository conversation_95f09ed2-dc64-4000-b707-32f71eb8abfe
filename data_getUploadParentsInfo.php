<?php  

/*

    error_reporting(E_ALL);  
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);
*/
 
    
    require_once("db_GetSetData.php");

    
    include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');

   
    $FileName = $_POST['FileName'];  

    $inputFileType = 'Excel2007';
     $sheetname = 'Sheet1';


    $inputFileName = '../pr/'.$FileName;

 
    /**  Create a new Reader of the type defined in $inputFileType  **/
    $objReader = PHPExcel_IOFactory::createReader($inputFileType);
    /**  Load $inputFileName to a PHPExcel Object  **/
    
    //$objReader->setLoadSheetsOnly($sheetname);
    $objReader->setReadDataOnly(true);


    $objPHPExcel = $objReader->load($inputFileName);


    $sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);  
    
     
    
    $i = 0; 
    $linecount = 0; 
    $matched_sessions = 0;
  

    foreach ($sheetData as &$row) {                              // read excel - start  

                $i++;


                $student_ext_id = $row["A"];
                $guardian_first_name = $row["E"];
                $guardian_last_name = $row["D"];
                $guardian_email = $row["G"];
                $guardian_phone = $row["F"];
            /*    
                $guardian_first_name = mysqli_real_escape_string($conn, $guardian_first_name); 
                $guardian_last_name = mysqli_real_escape_string($conn, $guardian_last_name); 

            */

                if (is_numeric($student_ext_id))  { 

                
                    echo $i.' $student_ext_id: '.$student_ext_id;
                    echo ' first name: '.$guardian_first_name;
                    echo ' last name: '.$guardian_last_name;
                    echo ' email: '.$guardian_email;
                    echo ' phone #: '.$guardian_phone.'</br>';

                    $conn = getCon();


                    $query = " UPDATE SchStudents
                                    SET GuardianFirstName = '{$guardian_first_name}',
                                        GuardianLastName = '{$guardian_last_name}',
                                        GuardianEmail = '{$guardian_email}',
                                        GuardianPhone = '{$guardian_phone}',
                                        UserId = '1',
                                        TransDate = NOW()
                                WHERE ExtId = '{$student_ext_id}'     
                            ";


                    $ret =  setData ($conn, $query);    

                     setDisConn($conn);    
                 
                 }

                
 
    }   //read excel - end 
 

   


    
  

    
 

     
?>