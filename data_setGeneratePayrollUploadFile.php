  
<?php
    

    require_once("db_GetSetData.php");

 

    $UserId = $_POST['UserId'];
    $PayrollType = $_POST['PayrollType'];

    $Data = $_POST['Data'];
    $Data=json_decode($Data,true);


    //$payroll_file = fopen("../hr/PayrollUploadFile.csv","w");
    $out_File = "../hr/PayrollUploadFile.csv";
    $fh = fopen($out_File, 'w') or die("can't open file");
   
    $linecount = 0;


    foreach ($Data as $PayrollData) {

      
      $linecount++;

      $EmployeeId = $PayrollData['EmployeeId'];
      $EmployeeIdOrig = $PayrollData['EmployeeId'];
      $EmployeeName = $PayrollData['EmployeeName'];
      $WorkDate = $PayrollData['WorkDate']; 
      $WorkDateUnf = $PayrollData['WorkDateUnf'];       
      $TotalHours = $PayrollData['TotalHours']; 
      $PayRate = $PayrollData['PayRate']; 
      $PayrollId = $PayrollData['PayrollId']; 
      $HrTypeId = $PayrollData['HrTypeId']; 


      if ($HrTypeId == '1') {

          $PayType = 'REG';

      } else {


          $PayType = '1099R';

      }
 

      //=======

     $conn = getCon();

 
      if ($PayrollType == 'Field') {


         $query = "UPDATE WeeklyServices       
             SET PaidFL = '1' 
           WHERE  RegistrantId =   '$EmployeeIdOrig' 
           AND    ServiceDate =   '$WorkDateUnf'  
           AND    ScheduleStatusId > 5

            ";
            


      } else {


         $query = "UPDATE OfficeEmployeesPayrollRecords       
             SET PaidFL = '1' 
           WHERE  OfficeEmployeeId = '$EmployeeIdOrig'
           AND    WorkDate =  '$WorkDateUnf'

              ";


      }

      

      $ret =  setData ($conn, $query);        
      setDisConn($conn);

      //=======

       $out_Line = $PayrollId.",".$EmployeeName.",E,".$PayType.",".$TotalHours.",".$PayRate.",,,,,,,,,,,,".$WorkDate.PHP_EOL; 

       fwrite($fh, $out_Line);
       fputcsv($payroll_file,$line,',');

       $out_Line = '';



  }   

   fclose($fh);

    
    echo  "{ success: true, transactions: '{$linecount}'}";

  
 
    


?>
 
