  
<?php
    
	/** Include path **/
	//set_include_path(get_include_path() . PATH_SEPARATOR . '../../Classes/');
	
	//include 'PHPExcel/IOFactory.php';
	require_once('DB.php');
	include('db_login.php');
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');
	

	$user_id = $_POST['UserId'];
	if (!$user_id) {
		$user_id = '1';
	}	

	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	//==================================
	// Get School Session Start/End Dates
	//==================================
 	
	 
	$query = "SELECT SchoolSeasonEndDate
		FROM SchSchoolYear			 
		WHERE CurrentYearFL = '1'";
	
	$result = $connection->query ($query);
	if (DB::isError($result)){
                die("Could not query the database:<br />$query ".DB::errorMessage($result));
    }

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
		$EndDate = $row['SchoolSeasonEndDate'];
	}	
	 
	/* Upload New File 
	 =============================================*/
		

	$inputFileName = '../uploads/mandates.xls';

/*
   if($ufile != none){ 
      
		//$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), "../hr/Resume.pdf");
		$rtn = move_uploaded_file (($_FILES["File"]["tmp_name"]), $inputFileName);
		
	} else {
		print "1:Error uploading extracted file. Please try again!!! "; 
	  
		echo  "{ success: error,  data: ".json_encode($file)."}";
	    Return ; 

	}
*/ 
	/**  Define a Read Filter class implementing PHPExcel_Reader_IReadFilter  */
	class chunkReadFilter implements PHPExcel_Reader_IReadFilter
	{
		private $_startRow = 0;

		private $_endRow = 0;

		/**  Set the list of rows that we want to read  */
		public function setRows($startRow, $chunkSize) {
			$this->_startRow	= $startRow;
			$this->_endRow		= $startRow + $chunkSize;
		}

		public function readCell($column, $row, $worksheetName = '') {
			//  Only read the heading row, and the rows that are configured in $this->_startRow and $this->_endRow
			if (($row >= $this->_startRow && $row < $this->_endRow)) {
				return true;
			}
			return false;
		}
	}

	$cnt = 0;	
	$linecount = 0;	


	$inputFileType = 'Excel5';

	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	
	
	//$objReader = new PHPExcel_Reader_Excel5();
	//$objReader->setReadDataOnly(true);
	//$objPHPExcel = $objReader->load($inputFileName);

	/**  Define how many rows we want to read for each "chunk"  **/
	$chunkSize = 1000;
	/**  Create a new Instance of our Read Filter  **/
	$chunkFilter = new chunkReadFilter();

	/**  Tell the Reader that we want to use the Read Filter that we've Instantiated  **/
	$objReader->setReadFilter($chunkFilter);
	$objReader->setReadDataOnly(true);

	/**  Loop to read our worksheet in "chunk size" blocks  **/
	for ($startRow = 1; $startRow <= 65536; $chunkSize) { // chunk size read - start 
		/**  Tell the Read Filter, the limits on which rows we want to read this iteration  **/
		$chunkFilter->setRows($startRow,$chunkSize);
		/**  Load only the rows that match our filter from $inputFileName to a PHPExcel Object  **/

		
		$objPHPExcel = $objReader->load($inputFileName);
		
		$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);

		$objPHPExcel->disconnectWorksheets(); 
		unset($objPHPExcel); 
		
		if (!$sheetData[$startRow]) {
			$startRow = 65536;	
		}

		$sheetDataActual =  array_slice($sheetData, $startRow-1, ($startRow += $chunkSize));
		//===============================
			foreach ($sheetDataActual as &$row) { // foreach - start

			$m_rec = substr(($row["A"]),0,1);

			if ($m_rec == '2') {

				/* Student External ID
				 ===================*/

				$student_ext_id = $row["A"];


				/* Student Last Name
				 ===================*/

				$student_last_name = $row["B"];


				/* Student First Name
				 ===================*/

				$student_first_name = $row["C"];


				/* Student DOB
				============================= */
				
				$dob = $row["H"];
				$date_unix_dob = date_create('30-12-1899');
    			date_add($date_unix_dob, date_interval_create_from_date_string("{$dob} days"));
    			$dob_str = date_format($date_unix_dob, 'Y-m-d');



				/* Student School ID
				 ===================*/

				$school_ext_id = '%'.$row["I"].'%';



				/* Student Service Type Desc
				 ================================*/

				$student_serv_type_desc = $row["O"];


				/* Student Language
				 ================================*/

				$student_language = $row["Q"];

				/* Mandate Ind/Grp Desc
				 ================================*/

				$mandate_ind_grp_desc = $row["R"];


				/* Mandate Group Size
				 ================================*/

				$mandate_grp_size = $row["S"];


				/* Mandate Service Freq
				 ================================*/

				$mandate_serv_freq = $row["T"];
				$mandate_serv_freq_num = filter_var($mandate_serv_freq, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Duration
				 ================================*/

				$mandate_serv_dur = $row["U"];
				$mandate_serv_dur_num = filter_var($mandate_serv_dur, FILTER_SANITIZE_NUMBER_INT);


				/* Mandate Service Start Date
				============================= */
				

				$start_date = $row["AD"];
				$date_unix = date_create('30-12-1899');
    			date_add($date_unix, date_interval_create_from_date_string("{$start_date} days"));
    			$start_date_str = date_format($date_unix, 'Y-m-d').'</br>';


				/* Provider Name
				 ================================*/

				$provider_name = explode(" ", $row["X"]);
				$max = sizeof($provider_name);
				$provider_first_name = strtoupper($provider_name[0]);
				$provider_last_name = strtoupper($provider_name[$max-1]);

				$provider_first_name = $connection->escapeSimple($provider_first_name); 
				$provider_last_name = $connection->escapeSimple($provider_last_name); 
				

				/* Mandate Status Desc
				 ================================*/

				$mandate_status_desc = $row["Z"];



				/*========================*/
				//================================ 
				//  Check if Student Exists  
				//================================ 
	 			
				$query5 = "SELECT 1 
								FROM SchStudents 
							WHERE ExtId = trim('{$student_ext_id}') ";
							
					
							
				$result5 = $connection->query ($query5);

				if (DB::isError($result5)){
					die("Could not query the database:<br />$query5 ".DB::errorMessage($result));
				}			

				
				//=======================
				// Add New Student
				//=======================
				
				
				if ($result5->numRows() == 0) {  // Start 1 
					
					
					$query1 = "INSERT INTO SchStudents
						(ExtId, 
						 SearchId, 
						 StatusId, 
						 SchoolId,
						 DateOfBirth, 
						 FirstName, 
						 LastName, 
						 UserId, 
						 TransDate) 
						VALUES 
						(
							'{$student_ext_id}',
							'{$student_ext_id}',
							'1',
							 COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '{$school_ext_id}' LIMIT 1),0),

							'{$dob_str}',
							'{$student_first_name}',
							'{$student_last_name}',
							'{$user_id}',
							now() )";
					
						
					$result1 = $connection->getAll($query1, DB_FETCHMODE_ASSOC);
			
					if (DB::isError($result1)){
								die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
					}
				} // End 1	


			//================================ 
			//  Check if Mandate Already Exists  
			//================================ 
		 	
			
			if (strlen($start_date_str) > 10) {

				$start_date_str = substr($start_date_str,0,10);	

			}

			$query4 = "SELECT 1 
						FROM SchStudentMandates a, SchServiceTypes b
					WHERE a.StudentExtId = '{$student_ext_id}'
					AND a.StartDate = '{$start_date_str}' 
					AND a.SessionFrequency = '{$mandate_serv_freq_num}' 
 					AND b.ServiceTypeDesc like '{$student_language_serv_type_desc}'	
 					AND a.SessionGrpSize = '{$mandate_grp_size}'
                    AND a.ServiceTypeId = b.Id 
                    AND a.StatusId = '1' 

                    ";
            
            echo '$query4: '.$query4.'</br>';        


			$result4 = $connection->query ($query4);

		 	
			if (DB::isError($result4)){
						die("Could not query the database:<br />$query4 ".DB::errorMessage($result));
			}			
		 	
			$mandate_exists =& $result4->numRows();			
		 
			if ($mandate_exists == 0)  {
					
			 
				//=======================
				// Add New Mandate
				//=======================
					$query2 = "INSERT INTO SchStudentMandates
						(	
							StudentId,
							StudentExtId,		
							SchoolId,
							StatusId,
							ServiceTypeId,
							RegistrantId,
							StartDate,
							EndDate,
							SECMandateStatus,
							SessionFrequency,
							SessionLength,
							SessionGrpSize,
							Language,
							DOEServiceTypeId,
							UserId,
							TransDate
						) 
						SELECT   
						 
						 	'0',
							'{$student_ext_id}',
							COALESCE((SELECT Id from SchSchools 
							  WHERE ExtId != '' 
							  AND ExtId like '%{$school_ext_id}%' LIMIT 1),0),
							'1',
							Id,

							COALESCE((SELECT Id from Registrants 
							  WHERE UPPER(FirstName) like '{$provider_first_name}'  
							  AND UPPER(LastName) like '{$provider_last_name}' LIMIT 1),0),
							
							'{$start_date_str}',
							'{$EndDate}',
							'{$mandate_status_desc}',

							'{$mandate_serv_freq_num}',
							'{$mandate_serv_dur_num}',
							'{$mandate_grp_size}',
							'{$student_language}',
							
							CASE '{$mandate_ind_grp_desc}' 
								WHEN 'Individual' THEN DOEServiceTypeIndId
								ELSE DOEServiceTypeGrpId
							END,

							'{$user_id}',
							now()  
						FROM SchServiceTypes
					WHERE ServiceTypeDesc like '{$student_serv_type_desc}' ";

					echo '$query2: '.$query2.'</br>'; 
						
						$result2 = $connection->getAll($query2, DB_FETCHMODE_ASSOC);

						if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result));
						}			


				//========================================================
				// Updade Student ID in the SchStudentMandates table
				//========================================================
				
				$query3 = "UPDATE SchStudents a,  SchStudentMandates b
                            SET b.StudentId = a.Id
                        WHERE b.StudentId = 0
                        AND  a.ExtId = b.StudentExtId  "; 		
 			
			
				$result3 = $connection->getAll($query3, DB_FETCHMODE_ASSOC);

			        if (DB::isError($result3)){
			            die("Could not query the database:<br />$query3 ".DB::errorMessage($result));
			        }

				}	        
		 
				/*=========================*/

			 	
			 
			}



		}	


	}	

	$connection->disconnect();
 
	$linecount = 1;

	echo  "{ success: true, transactions: '{$linecount}'}";

?>