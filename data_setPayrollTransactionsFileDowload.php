  
<?php
    

     $PayrollType = $_GET['PayrollType'];
     if ($PayrollType == 'Field') {

        $DownloadedFileName = 'PayrollUploadFile-'.Date('m-d-Y').'.csv';


      } else {

        $DownloadedFileName = 'OfficePayrollUploadFile-'.Date('m-d-Y').'.csv';


      }  
      $out_File = "../hr/PayrollUploadFile.csv";
 
 
      header('Content-Description: File Transfer');
      header('Content-Type: application/octet-stream');
      //header('Content-Disposition: attachment; filename='.basename($out_File));
      header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
      
      header('Content-Transfer-Encoding: binary');
      header('Expires: 0');
      header('Cache-Control: must-revalidate');
      header('Pragma: public');
      header('Content-Length: ' . filesize($out_File));
      ob_clean();
      flush();
      readfile($out_File);
      exit;


/*


    $out_File = "../uploads/eweb_to_payroll_upload.xlsx";

    $DownloadedFileName = 'eWebStaffing-PayrollUploadFile-'.Date('m-d-Y').'.xlsx';
 
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    //header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Disposition: attachment; filename="' . $DownloadedFileName . '"');        
    
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);

    unlink($out_File);    

    exit;
*/

?>