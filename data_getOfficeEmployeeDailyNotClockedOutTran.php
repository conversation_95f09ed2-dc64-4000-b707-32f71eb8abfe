<?php 

	require_once("db_GetSetData.php");

	$WorkDate = $_GET['WorkDate'];  
	$OfficeEmployeeId = $_GET['OfficeEmployeeId'];  


	$conn = getCon();

    $query = "	 SELECT Id as PayrollRecordId,
    					
    					DATE_FORMAT(  WorkDate, '%m-%d-%Y' ) AS WorkDate, 
 						DATE_FORMAT( ClockInTime, '%l:%i %p' ) as ClockInTimeDisp,
            			ClockInTime 

				 from    OfficeEmployeesPayrollRecords  
				    WHERE OfficeEmployeeId = '{$OfficeEmployeeId}'  
				    AND WorkDate = '{$WorkDate}'
                    ANd ClockInTime != '00:00:00' 
				    AND ClockOutTime = '00:00:00'   

			"; 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;	  

?>
