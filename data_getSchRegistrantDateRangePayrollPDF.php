<?php 
	
  require_once("db_GetSetData.php");
  require('fpdf/fpdf.php'); 

  $conn = getCon();

  $RegistrantId = $_GET['RegistrantId'];  
  $RegistrantName = $_GET['RegistrantName'];  
  $FromDate = $_GET['FromDate'];
  $ToDate = $_GET['ToDate'];

  //var_dump($_GET);

  $GLOBALS['RegistrantName'] = $RegistrantName;
  //echo '$RegistrantName: '.$RegistrantName;

  $GLOBALS['FromDateFrm'] = date('m/d/Y', strtotime($FromDate));
  $GLOBALS['ToDateFrm'] = date('m/d/Y', strtotime($ToDate));




  class PDF extends FPDF
    {


    function PDF($orientation='L',$unit='mm',$format='A4')
    
    {
      //Call parent constructor
      $this->FPDF($orientation,$unit,$format);
    }




    //Page header
    function Header()
      { 
        $this->SetLeftMargin(5);
        $this->SetRightMargin(5);

        $this->Ln(4);

        $this->SetFont('Arial','B',14);

        $this->Cell(0,5,'Date Range Payroll Report',0,1,'C');
        $this->Ln(2);

        $this->SetFont('Arial','B',12);

        $this->Cell(0,5,'Registrant Name: '.$GLOBALS['RegistrantName'],0,1,'C');
        $this->Cell(0,5,'Date Range: '.$GLOBALS['FromDateFrm'].' - '.$GLOBALS['ToDateFrm'],0,1,'C');

        $this->Ln(2);
        $this->SetFont('Arial','B',9);
 
         $this->Cell(10,4,'LN #',1,0,'C');
         $this->Cell(20,4,'Date',1,0,'C');
         $this->Cell(20,4,'Start Time',1,0,'C');
         $this->Cell(20,4,'End Time',1,0,'C');
         $this->Cell(10,4,'G.S.',1,0,'C');
         $this->Cell(12,4,'Dur.',1,0,'C');
         $this->Cell(15,4,'Pay Rate',1,0,'C');         
         $this->Cell(15,4,'Pay Amt',1,0,'C');                  
         $this->Cell(145,4,'Student(s)',1,1,'C');

      }

      //Page footer
      function Footer()
      {
        //Position at 1.5 cm from bottom
        $this->SetY(-15);
        //Arial italic 8
        
        $this->SetFont('Arial','B',11);
        //$this->SetX(5);
        $this->Cell(0,5,'_______________________________________________________________________________________',0,1,'C');

        $this->SetFont('Arial','I',8);
        //Page number
        $this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
      }
    }


    //Instanciation of inherited class
    $pdf=new PDF();
    $pdf->AliasNbPages();
    //$pdf->AddPage();

    //$pdf->SetFont('Times','',12);
    $pdf->SetFont('Arial','',10);





    $query = "	SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      TotalHours,
                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
                      
                      a.SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      SchoolName,
                      PaidFL,
                      case 
                        when SpecialPayRate > 0 then SpecialPayRate
                        else TherapyPayRate
                      end as PayRate,
                      


                      case 
                        when SpecialPayRate > 0 then SpecialPayRate * TotalHours
                        else TherapyPayRate * TotalHours
                      end as PayAmount 
                       


                  FROM  WeeklyServices a, SchStudents b, ScheduleStatuses g, SchSchools d, SchStudentMandates e, Registrants f 
                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   b.Id = a.StudentId
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   a.MandateId = e.Id 
                        AND   a.SessionTypeId = 0
                        AND   a.RegistrantId = f.Id  
                   GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours

           union       
   SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      TotalHours,
                      
                      CONCAT('Evaluation (',EvalStudentName,')') as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      '' as SchoolName,
                      PaidFL,
                      EvalPayRate as PayRate,
                      EvalPayRate * TotalHours as PayAmount  
                  FROM  WeeklyServices a,   ScheduleStatuses g, Registrants f  
                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SessionTypeId = 1 
                        AND   a.RegistrantId = f.Id  

           union       
   SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      TotalHours,
                      
                       CONCAT('Supervision (',b.FirstName,' ',b.FirstName,')') as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      '' as SchoolName,
                      PaidFL,
                      SupervisionPayRate as PayRate,
                      SupervisionPayRate * TotalHours as PayAmount
                  FROM  WeeklyServices a,   ScheduleStatuses g, Registrants b  
                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SessionTypeId = 2 
                        AND   a.SupervisedCFId = b.Id
        union

SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      TotalHours,
                      
                       '' as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      SchoolName,
                      PaidFL,
                      case SessionTypeId
            when '0' then TherapyPayRate 
            when '2' then SupervisionPayRate 
            when '1' then EvalPayRate 
            when '3' then NotesWritingPayRate 
                      end  as  PayRate,
                      
                      case SessionTypeId
            when '0' then TherapyPayRate * TotalHours
            when '2' then SupervisionPayRate * TotalHours
            when '1' then EvalPayRate * TotalHours
            when '3' then NotesWritingPayRate * TotalHours
                      end  as  PayAmount
                       
                  FROM  WeeklyServices a,   ScheduleStatuses g, Registrants b, SchSchools d  
                         WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                       AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = 4071 
                        AND   a.SchoolId = d.Id
                        AND   a.RegistrantId = b.Id
                  ";

  $result =  mysqli_query($conn, $query) or die
  ("Error in Selecting " . mysqli_error($conn));
   
  $j = 0;
  $i = 1;    

  while ($row = $result->fetch_assoc()) {

        
        $cur_y = $pdf->GetY();  

       if (($j == 0) || ($cur_y > 165)) {

           $pdf->AddPage();
           $j = 0; 

       }
       
       $j++;

      if ($saved_school_name != $row['SchoolName']) {

          $saved_school_name = $row['SchoolName'];      


         // Total Hours By School
         if (isset($school_total_hours)  ) {

            $pdf->Ln(3);

             $school_total_hours = number_format($school_total_hours,2,'.',',');            
             //$school_total_pay = $school_total_hours * 50;   
             $school_total_pay = number_format($school_total_pay,2,'.',',');            

             $pdf->SetFont('Arial','B',9);
             $pdf->Cell(75,4,'Total Hours: ',0,0,'R');
             $pdf->Cell(17,4,$school_total_hours,1,0,'C');
             $pdf->Cell(35,4,'Total Pay: ',0,0,'R');
             $pdf->Cell(17,4,$school_total_pay,1,1,'C');


             $pdf->Ln(3);



             $grand_total_hours += $school_total_hours;              
             $grand_total_pay += $school_total_pay;              

             $school_total_hours = 0;              
             $school_total_pay = 0;              


         }



        //Line break
         $pdf->Ln(3);

         
         $pdf->SetFont('Arial','B',9);

         $pdf->Cell(0,5,'School: '.$row['SchoolName'],0,1,'L');



/*
         //Set Table Header
         $pdf->Cell(10,4,'LN #',1,0,'C');
         $pdf->Cell(20,4,'Date',1,0,'C');
         $pdf->Cell(20,4,'Start Time',1,0,'C');
         $pdf->Cell(20,4,'End Time',1,0,'C');
         $pdf->Cell(10,4,'G.S.',1,0,'C');
         $pdf->Cell(12,4,'Dur.',1,0,'C');
         $pdf->Cell(175,4,'Student(s)',1,1,'C');
         //$pdf->Cell(25,4,'Contract',1,1,'C');
*/
          //$this->Ln(10);
          $pdf->SetFont('Arial','',8);



      }



        $student_mames = $row['StudentName'];

         
         /*
         if (strlen($student_mames) > 40) {

            $student_mames = substr($student_mames,0,40).'...';

         }  
        */ 

         $pay_amount = '$'.number_format($row['PayAmount'],2,'.',',');        

         $pdf->Cell(10,4,$i++,1,0,'C');
         $pdf->Cell(20,4,$row['ServiceDate'],1,0,'C');
         $pdf->Cell(20,4,$row['StartTime'],1,0,'C');
         $pdf->Cell(20,4,$row['EndTime'],1,0,'C');
         $pdf->Cell(10,4,$row['SessionGrpSize'],1,0,'C');
         $pdf->Cell(12,4,$row['TotalHours'],1,0,'C');
         $pdf->Cell(15,4,'$'.$row['PayRate'],1,0,'C');
         $pdf->Cell(15,4,$pay_amount,1,0,'C');

         $pdf->Cell(145,4,$student_mames,1,1,'L');
         //$pdf->Cell(25,4,$row['BillingContractDesc'],1,1,'L');




         $school_total_hours = $school_total_hours + ($row['TotalHours']);  
         $school_total_pay = $school_total_pay +   ($row['PayAmount']);  


  }
  

         if (isset($school_total_hours)  ) {

            $pdf->Ln(3);

             $school_total_hours = number_format($school_total_hours,2,'.',',');            
             //$school_total_pay = $school_total_hours * 50;   
             $school_total_pay = number_format($school_total_pay,2,'.',',');            

             $pdf->SetFont('Arial','B',9);
             $pdf->Cell(75,4,'Total Hours: ',0,0,'R');
             $pdf->Cell(17,4,$school_total_hours,1,0,'C');
             $pdf->Cell(35,4,'Total Pay: ',0,0,'R');
             $pdf->Cell(17,4,'$'.$school_total_pay,1,1,'C');

             $pdf->Ln(3);

             $grand_total_hours += $school_total_hours;  
             $grand_total_hours = number_format($grand_total_hours,2,'.',',');            

             $grand_total_pay  += $school_total_pay;   
             $grand_total_pay = number_format($grand_total_pay,2,'.',',');            

             $pdf->SetFont('Arial','B',9);
             $pdf->Cell(75,4,'Grand Total Hours: ',0,0,'R');
             $pdf->Cell(17,4,$grand_total_hours,1,0,'C');
             $pdf->Cell(35,4,'Grand Total Pay: ',0,0,'R');
             $pdf->Cell(17,4,'$'.$grand_total_pay,1,1,'C');

             $pdf->Ln(3);

         }



  $pdf->Output();

  setDisConn($conn);


?>

