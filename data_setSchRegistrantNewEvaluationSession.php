<?php 

	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$RegistrantId = $_POST['RegistrantId'];
	$ServiceDate = $_POST['ServiceDate'];
    $PayrollWeek = $_POST['PayrollWeek'];
	$WeekDay = $_POST['WeekDay'];
	$StartTime = $_POST['StartTime'];
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$EvalStudentName = $_POST['EvalStudentName'];
	$UserId = $_POST['UserId'];



	$EvalStudentName = mysqli_real_escape_string($conn, $EvalStudentName); 


	$query ="INSERT INTO WeeklyServices
							                  (
												PayrollWeek,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												RegistrantId, 
												EvalStudentName,
												SessionTypeId,
												UserId,
												TransDate )	
												
						    VALUES  (


										'{$PayrollWeek}',
										'7',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
                                        '{$RegistrantId}',
										'{$EvalStudentName}',
										'1',
 									 	'{$UserId}',
                                        NOW()  


						    )
						    ";



	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	echo $ret;
	//echo $query;


?>



