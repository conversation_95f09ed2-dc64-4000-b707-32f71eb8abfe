<?php 

	require_once("db_login.php");
  
 	

	$login = ($_POST["login"]); 
	$password = ($_POST["password"]); 

	$conn=mysqli_connect($db_hostname, $db_username, $db_password,$db_database);
	// Check connection
	if (mysqli_connect_errno())
	  {
	  echo "Could not query the database: " . mysqli_connect_error();
	  }

 

	$login = mysqli_real_escape_string($conn, $login); 
	$password = mysqli_real_escape_string($conn, $password); 

    $query = "SELECT  	Id as OfficeEmployeeId, 
            FirstName,
            LastName 
			 from OfficeEmployees  
			 where upper(LastName ) = upper('{$login}')
			 and SSNumber  = '{$password}'
			 and StatusId = '1'  ";

	$result =  mysqli_query($conn, $query) or die
	("Error in Selecting " . mysqli_error($conn));
	

	if (mysqli_num_rows($result) == "1") {

		$rows = array();
		while ($row = $result->fetch_assoc()) {
		    $rows[] = $row;
		}


        $OfficeEmployeeName =  $rows[0]['FirstName']." ". $rows[0]['LastName']; 
        $arr = Array(	
        				"UserId" => $rows[0]['OfficeEmployeeId'],        				
        				"OfficeEmployeeId" => $rows[0]['OfficeEmployeeId'],
					 	"UserName" => $OfficeEmployeeName, 
					 	"OfficeEmployeeName" => $OfficeEmployeeName 

					);


  

	} else {

        $invalid = "invalid";
        $arr = Array("UserId" => $invalid, "UserName" => $invalid);  



	}	

  	
	if ($arr["UserId"] == "invalid") {
		echo  "{ success: false}";
	} else {
		echo  "{ success: true,  data: ".json_encode($arr)."}";
	}
 
 

    mysqli_free_result($result);
	mysqli_close($conn);


?>
