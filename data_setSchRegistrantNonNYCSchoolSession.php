<?php 

	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$SessionId = $_POST['SessionId'];
	$RegistrantId = $_POST['RegistrantId'];	
	$ServiceDate = $_POST['ServiceDate'];
    $PayrollWeek = $_POST['PayrollWeek'];
	$WeekDay = $_POST['WeekDay'];
	$StartTime = $_POST['StartTime'];
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$SessionTypeId = $_POST['SessionTypeId'];
	$UserId = $_POST['UserId'];


	if (!$SessionId) {

							$query ="INSERT INTO WeeklyServices
							                  (
												PayrollWeek,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												RegistrantId, 
												SchoolId,
												SessionTypeId,
												UserId,
												TransDate )	
												
						    VALUES  (


										'{$PayrollWeek}',
										'7',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
                                        '{$RegistrantId}',
										(SELECT Id as SchoolId from SchSchools 
     										where DistrictId = '34' limit 1), 
										'{$SessionTypeId}',
 									 	'{$UserId}',
                                        NOW()  


						    )
						    ";


	} else {

							$query ="UPDATE WeeklyServices
							               
 										SET StartTime = '{$StartTime}', 
											EndTime  ='{$EndTime}',		
											TotalHours ='{$TotalHours}',  
											SessionTypeId ='{$SessionTypeId}', 
											UserId   = '{$UserId}', 
											TransDate = NOW()	
									WHERE Id = 	'{$SessionId}'				
						    
						    ";

	}

 			



	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	//echo $ret;
	echo $query;


?>



