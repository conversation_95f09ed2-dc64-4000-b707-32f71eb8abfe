<?php 

	require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$ServiceDate = $_GET['ServiceDate'];  

    $query = "	(SELECT a.Id as SessionId,  
    					DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 	
						DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
						FORMAT((a.TotalHours * 60), 0) as TotalHours,
 						'Evalution' as SessionTypeDesc,
 						EvalStudentName,
 						'' as SupervisedCFName   	

				FROM 	WeeklyServices a 
						WHERE a.RegistrantId = '{$RegistrantId}' 
						AND   a.ServiceDate =  '{$ServiceDate}'  
						AND   a.ScheduleStatusId > 6
						AND   a.SessionTypeId = 1 )
			UNION 		
				(SELECT a.Id as SessionId,
						DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime, 	
						DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
						FORMAT((a.TotalHours * 60), 0) as TotalHours,
 						'Supervision' as SessionTypeDesc,
 						'' as EvalStudentName,
 						CONCAT( trim( b.LastName) , ', ', trim(b.FirstName))  as SupervisedCFName   	

				FROM 	WeeklyServices a, Registrants b 
						WHERE a.RegistrantId = '{$RegistrantId}' 
						AND   a.ServiceDate =  '{$ServiceDate}'  
						AND   a.ScheduleStatusId > 6
						AND   a.SessionTypeId = 2 
						AND   a.SupervisedCFId = b.Id )

				Order By StartTime, EndTime		
				      	  	";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;


  
?>
