<?php  

/*

    error_reporting(E_ALL);  
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);
*/
 
 
 

  require_once("db_GetSetData.php");
  $conn = getCon();

 

      $query = "SELECT Id, MandateId 
              from SchRsa7aFormSignatures a 
          WHERE    ParentApprovalSentEmailFL = '1'
          Order by a.MandateId  limit 500


         ";



    $result =  mysqli_query($conn, $query) or die
    ("Error in Selecting " . mysqli_error($conn));
   
 
    $i = 0;

         $url_qb = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setGenerateApprovalEmailAll.php";
         echo 'url_qb: '.$url_qb.'</br>'; 



    while ($row = $result->fetch_assoc()) { // While - Start
      
    $i++; 
  

        echo ' Mandate: '.$row['MandateId'].'</br>';
     
      
        $fields_qb = array('MandateId' => $row['MandateId'],
                           'FromDate' => '2020-04-01',
                           'ToDate' => '2020-04-30'
                            
                );
                          

        $curl = curl_init();
       

         
        curl_setopt($curl, CURLOPT_URL, $url_qb);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $fields_qb); 
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false

        // execute and return string (this should be an empty string '')
        $str = curl_exec($curl);

        echo '$str :'.$str;
        
        curl_close($curl);
     
        sleep(3);


        //================

        
        $rsa_7a_id = $row['Id'];

        $conn1 = getCon();

        $query1 = "  
                UPDATE SchRsa7aFormSignatures a 
                  SET ParentApprovalSentEmailFL = '0',
                       ParentApprovalSentEmailDate = NOW()

            WHERE    Id = '{$rsa_7a_id}'
 

             ";



          $result1 =  mysqli_query($conn1, $query1) or die
          ("Error in Selecting " . mysqli_error($conn1));

          setDisConn($conn1);

        //================

       

      } // While - End

      setDisConn($conn);

  

     
?>