<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];  
    $ToDate = $_GET['ToDate'];  
	$SessionTypeId = $_GET['SessionTypeId'];
 

    $query = " SELECT /*+ INDEX(a idx_service_schedule_session_district) */
               a.Id as SessionId,
               CONCAT(TRIM(f.FirstName), ' ', TRIM(f.LastName)) AS RegistrantName,
               DATE_FORMAT(a.ServiceDate, '%m-%d-%Y') AS ServiceDate,
               DATE_FORMAT(a.ServiceDate, '%m-%d-%Y') AS SessionDate,
               DATE_FORMAT(a.StartTime, '%l:%i %p') as StartTime,
               DATE_FORMAT(a.EndTime, '%l:%i %p') as EndTime,
               TotalHours,
               a.RegistrantId,
               WeekDay 
               SessionTypeId,
               CASE SessionTypeId
                  WHEN '1' THEN 'Evaluation'
                  WHEN '3' THEN 'Notes Writing'
                  WHEN '4' THEN 'Admin'
                  WHEN '5' THEN 'Sick Time'
                  WHEN '6' THEN 'Lactation Time'
                  WHEN '7' THEN 'Jury Duty'
               END as SessionTypeDesc,
               CONCAT(TRIM(e.FirstName), ' ', TRIM(e.LastName)) AS UserName,
               DATE_FORMAT(a.TransDate, '%m-%d-%Y %r') as TransDate
            FROM WeeklyServices a 
            JOIN SchSchools d ON a.SchoolId = d.Id
            JOIN Users e ON a.UserId = e.UserId
            JOIN Registrants f ON a.RegistrantId = f.Id
            WHERE a.ServiceDate BETWEEN '{$FromDate}' AND '{$ToDate}'
            AND a.ScheduleStatusId > 6
            AND a.SessionTypeId > 0
            AND d.DistrictId = 34
            and a.SessionTypeId = '{$SessionTypeId}'
            ORDER BY a.RegistrantId, a.SessionTypeId, a.ServiceDate;


                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

