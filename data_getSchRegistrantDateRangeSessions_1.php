<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


    $query = " ( 	SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      a.TotalHours,
                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
                      
                      a.SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      SchoolName,
                      PaidFL 
                  FROM  WeeklyServices a, SchStudents b, ScheduleStatuses g, SchSchools d, SchStudentMandates e 
                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   b.Id = a.StudentId
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   a.MandateId = e.Id 
                        AND   a.SessionTypeId = 0
                   GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours
                    Order By a.ServiceDate
               )    
           union       
   (   SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      a.TotalHours,
                      
                      CONCAT('Evaluation (',EvalStudentName,')') as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      '' as SchoolName,
                      PaidFL 
                  FROM  WeeklyServices a,   ScheduleStatuses g  
                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SessionTypeId = 1 
                         Order By a.ServiceDate
    ) 
           union       
   
   (         
   SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      a.TotalHours,
                      
                       CONCAT('Supervision (',b.FirstName,' ',b.FirstName,')') as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      '' as SchoolName,
                      PaidFL 
                  FROM  WeeklyServices a,   ScheduleStatuses g, Registrants b  
                        WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SessionTypeId = 2 
                        AND   a.SupervisedCFId = b.Id
                         Order By a.ServiceDate  
   )
           union       
 ( 
SELECT  DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                       a.TotalHours ,
                      
                       SchoolName  as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      SchoolName,
                      PaidFL 
                  FROM  WeeklyServices a,   ScheduleStatuses g, Registrants b, SchSchools d  
                         WHERE a.RegistrantId = '{$RegistrantId}' 
                        AND   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                       AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = '4071' 
                        AND   a.RegistrantId = b.Id
                        AND   a.SchoolId = d.Id
                         Order By a.ServiceDate

   )                     
                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

  //echo $ret;
	echo $query;

?>

