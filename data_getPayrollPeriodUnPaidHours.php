<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];

    sleep(5);

$query = "call  proc_getPayrollPeriodUnPaidHours ('{$FromDate}','{$ToDate}' ) ";
/*
   $query = "SELECT 
                      CONCAT( trim( c.FirstName) , ' ', trim(c.LastName)) as RegistrantName,

                      a.RegistrantId,
                      c.PayrollId,
                      DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                      a.ServiceDate AS ServiceDateUnf,
                      a.StartTime,
                      a.EndTime,
                      'Therapy' as SessionTypeDesc,
                      TherapyPayRate as  PayRate,
                      sum(a.TotalHours) as TotalHours
                    FROM  WeeklyServices a, 
                           Registrants c  
                       WHERE   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                          AND   a.RegistrantId = c.Id 
                        AND   a.PaidFL = '0' 
                         AND   a.SessionTypeId ='0'  
                   GROUP BY c.LastName, c.FirstName,  ServiceDateUnf, a.StartTime, a.EndTime
      union             
 SELECT 
                      CONCAT( trim( c.FirstName) , ' ', trim(c.LastName)) as RegistrantName,
                      a.RegistrantId,
                      c.PayrollId,
                      DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                      a.ServiceDate AS ServiceDateUnf,
                      a.StartTime,
                      a.EndTime,
                      'Evaluation' as SessionTypeDesc,
                      EvalPayRate as  PayRate,
                      sum(a.TotalHours) as TotalHours
                    FROM  WeeklyServices a, 
                           Registrants c  
                        WHERE   a.ServiceDate between '{$FromDate}' AND '{$ToDate}'  
                        AND   a.ScheduleStatusId > 5
                          AND   a.RegistrantId = c.Id 
                        AND   a.PaidFL = '0' 
                         AND   a.SessionTypeId ='1'  
                   GROUP BY c.LastName, c.FirstName,  ServiceDateUnf, a.StartTime, a.EndTime 
            union       
 SELECT 
                      CONCAT( trim( c.FirstName) , ' ', trim(c.LastName)) as RegistrantName,
                      a.RegistrantId,
                      c.PayrollId,
                      DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                      a.ServiceDate AS ServiceDateUnf,
                      a.StartTime,
                      a.EndTime,
                      
                      'Supervision' as SessionTypeDesc,
                      SupervisionPayRate as  PayRate,
                      sum(a.TotalHours) as TotalHours
                    FROM  WeeklyServices a, 
                           Registrants c  
                        WHERE   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                          AND   a.RegistrantId = c.Id 
                        AND   a.PaidFL = '0' 
                         AND   a.SessionTypeId ='2'  
                   GROUP BY c.LastName, c.FirstName,  ServiceDateUnf , a.StartTime, a.EndTime 
                   ";
*/
     

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

