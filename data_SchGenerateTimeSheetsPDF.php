
<?php
 
/*
  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);
*/

	require_once("db_login.php");
	require_once('DB.php');
	require('fpdf/fpdf.php');


    $connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
   if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 


	$RegistrantId = $_GET['RegistrantId'];
	$RegistrantName = $_GET['RegistrantName'];

	$ServiceTypeDesc = $_GET['ServiceTypeDesc'];
	$ExtId = $_GET['ExtId'];

	$SessionTypeId = $_GET['SessionTypeId'];


	//$Month = $_GET['Month'];
	//$Year = $_GET['Year'];
	
	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


	$GLOBALS['ServiceTypeDesc'] = $ServiceTypeDesc;
	$GLOBALS['RegistrantName'] = $RegistrantName;
	$GLOBALS['ExtId'] = $ExtId;


	class PDF extends FPDF
	{

	 
		function PDF($orientation='L',$unit='mm',$format='A4')
		
		{
			//Call parent constructor
			$this->FPDF($orientation,$unit,$format);
		}
	
	 
		//Page header
		function Header()
		{	
			$this->SetLeftMargin(5);
			$this->SetRightMargin(5);

			//Logo
			//$this->Image('logo.jpg',5,5,30);
			//Arial bold 15
			$this->SetFont('Arial','',10);
		 	
			
	  
			$this->Ln(2);			
			$this->Cell(5,4,'',0,0,'C');
			$this->Cell(70,4,'Legendary Therapy ',0,0,'L');
			$this->Cell(30,4,'Provider Name: ',0,0,'L');
			$this->Cell(50,4,$GLOBALS['RegistrantName'],0,0,'L');
			$this->Cell(10,4,'SS#: ',0,0,'L');
			$this->Cell(30,4,$GLOBALS['ExtId'],0,1,'L');

			$this->Ln(2);
			$this->Cell(5,4,'',0,0,'C');			
			$this->Cell(70,4,'997 Stafford Ave, Staten Island NY 10309',0,0,'L');
			$this->SetFont('Arial','I',10);
			
			$this->SetTextColor(0,0,255);
			$this->Cell(40,4,'<EMAIL>',0,1,'L');
			$this->SetTextColor(0,0,0);			
			$this->SetFont('Arial','',10);


			$this->Ln(2);
			$this->Cell(5,4,'',0,0,'C');			
			$this->Cell(70,4,'************',0,0,'L');
			$this->Cell(20,4,'Location:',0,1,'L');

			$this->SetFont('Arial','B',10);

			$this->Ln(2);
			$this->Cell(5,4,'',0,0,'C');			
			$this->Cell(70,4,'TAX ID#: 20-8761785',0,0,'L');
			$this->SetFont('Arial','',10);

			$this->Cell(40,4,'Language: English',0,1,'L');

			$this->Ln(2);
			$this->Cell(5,4,'',0,0,'C');			
			
			$this->SetFont('Arial','I',10);			
			$this->SetTextColor(0,0,255);
			$this->Cell(70,4,'<EMAIL>',0,0,'L');
			$this->SetTextColor(0,0,0);			
			$this->SetFont('Arial','',10);

			$this->Cell(70,4,'Related Services: '.$GLOBALS['ServiceTypeDesc'],0,1,'L');


 			$this->Ln(10);
			$this->SetFont('Arial','B',9);
			$this->Cell(5,4,'',0,0,'C');			
			$this->Cell(25,4,'Student Id',1,0,'L');
			$this->Cell(40,4,'Last Name',1,0,'L');
			$this->Cell(30,4,'First Name',1,0,'L');
			$this->Cell(15,4,'DOB',1,0,'L');
			$this->Cell(15,4,'Date',1,0,'L');
			$this->Cell(20,4,'Start Time',1,0,'L');
			$this->Cell(20,4,'End Time',1,0,'L');
			$this->Cell(15,4,'Duration',1,0,'L');
			$this->Cell(15,4,'Grp Size',1,0,'L');
			$this->Cell(20,4,'Mandate',1,0,'L');
			$this->Cell(25,4,'Del. Method',1,1,'L');

 


			//=============================


	 	}

		//Page footer
		function Footer()
		{
			//Position at 1.5 cm from bottom
			$this->SetY(-15);
			//Arial italic 8
			$this->SetFont('Arial','I',8);
			//Page number
			$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
		}
	}




	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	//$pdf->SetFont('Times','',12);
	$pdf->SetFont('Arial','',9);



    // All Sesions
    //======================= 
    if ($SessionTypeId == 0) {  

		 	$query = "SELECT    DISTINCT    b.ExtId,
		 							b.FirstName,
		                			b.LastName, 
					                a.SchoolId,
					                SchoolName,
					                DATE_FORMAT( b.DateOfBirth, '%m/%d/%y' ) AS DOB,
									a.ServiceDate as ServiceDateSort,
		                        	DATE_FORMAT( a.ServiceDate, '%m/%d/%y' ) AS ServiceDate,
		                        	a.StartTime as StartTimeSort,
		                        	DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
		                        	DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
		                        	a.SessionGrpSize,
		                        	ROUND((TotalHours * 60)) as Duration,
		                        	CONCAT( c.SessionFrequency , ' X ', c.SessionLength , ' X ', c.SessionGrpSize ) AS MandateDesc,
		                        	a.SessionDeliveryModeId
				                 
				FROM WeeklyServices a, 
				     SchStudents b,
				     SchStudentMandates c,
				     SchSchools d 
			WHERE a.RegistrantId = '{$RegistrantId}' 
				AND   a.ScheduleStatusId >= 7    
		 	    AND a.ServiceDate between '{$FromDate}' and '{$ToDate}'
		 	    AND a.SchoolId = d.Id

				AND b.Id = a.StudentId
				AND a.MandateId = c.Id
			Order BY SchoolId, b.LastName, b.FirstName, ServiceDateSort, 	StartTimeSort
				"; 
    }
	
    // In-Persion Sesions
    //======================= 
    if ($SessionTypeId == 1) {  

		 	$query = "SELECT    DISTINCT    b.ExtId,
		 							b.FirstName,
		                			b.LastName, 
					                a.SchoolId,
					                SchoolName,
					                DATE_FORMAT( b.DateOfBirth, '%m/%d/%y' ) AS DOB,
									a.ServiceDate as ServiceDateSort,
		                        	DATE_FORMAT( a.ServiceDate, '%m/%d/%y' ) AS ServiceDate,
		                        	a.StartTime as StartTimeSort,
		                        	DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
		                        	DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
		                        	a.SessionGrpSize,
		                        	ROUND((TotalHours * 60)) as Duration,
		                        	CONCAT( c.SessionFrequency , ' X ', c.SessionLength , ' X ', c.SessionGrpSize ) AS MandateDesc,
		                        	a.SessionDeliveryModeId
				                 
				FROM WeeklyServices a, 
				     SchStudents b,
				     SchStudentMandates c,
				     SchSchools d 
			WHERE a.RegistrantId = '{$RegistrantId}' 
				AND   a.ScheduleStatusId >= 7    
		 	    AND a.ServiceDate between '{$FromDate}' and '{$ToDate}'
		 	    AND a.SchoolId = d.Id
		 	    AND a.SessionDeliveryModeId = 'I'  
				AND b.Id = a.StudentId
				AND a.MandateId = c.Id
			Order BY SchoolId, b.LastName, b.FirstName, a.SessionGrpSize, ServiceDateSort, 	StartTimeSort
				"; 
    }

    // Tele-Therapy Sesions
    //======================= 
    if ($SessionTypeId == 2) {  

		 	$query = "SELECT   DISTINCT     b.ExtId,
		 							b.FirstName,
		                			b.LastName, 
					                a.SchoolId,
					                SchoolName,
					                DATE_FORMAT( b.DateOfBirth, '%m/%d/%y' ) AS DOB,
									a.ServiceDate as ServiceDateSort,
		                        	DATE_FORMAT( a.ServiceDate, '%m/%d/%y' ) AS ServiceDate,
		                        	a.StartTime as StartTimeSort,
		                        	DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
		                        	DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
		                        	a.SessionGrpSize,
		                        	ROUND((TotalHours * 60)) as Duration,
		                        	CONCAT( c.SessionFrequency , ' X ', c.SessionLength , ' X ', c.SessionGrpSize ) AS MandateDesc,
		                        	a.SessionDeliveryModeId
				                 
				FROM WeeklyServices a, 
				     SchStudents b,
				     SchStudentMandates c,
				     SchSchools d 
			WHERE a.RegistrantId = '{$RegistrantId}' 
				AND   a.ScheduleStatusId >= 7    
		 	    AND a.ServiceDate between '{$FromDate}' and '{$ToDate}'
		 	    AND a.SchoolId = d.Id
		 	    AND a.SessionDeliveryModeId != 'I'  
				AND b.Id = a.StudentId
				AND a.MandateId = c.Id
			Order BY SchoolId, b.LastName, b.FirstName, a.SessionGrpSize, ServiceDateSort, 	StartTimeSort
				"; 
    }

	//echo '$query: '.$query;

	$result = $connection->query($query);
	 	 
	//++++++++++++++++++++++++++++++++++++++++++++++
	
	
	$i = 0; 
	$saved_school_id = '';

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {
	 	
			if (!$saved_school_id) {

				$saved_school_id = $row['SchoolId'];
				$saved_school_name = $row['SchoolName'];

			}

			if ($saved_school_id != $row['SchoolId']) {



			$pdf->SetFont('Arial','',8);

		 	$pdf->Ln(10);
			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'I hereby certify that I have provided related services on the dates for the duration indicated herein.',0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'By my signature I acknowledge that I have review this Related.',0,1,'L');

			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'I understand that when completed and filed, this form becomes a record of the Board of Education',0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'Service billing form and that, to be best of my knowlerge, these',0,1,'L');

			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'and that any material misreprecentation may subject me to criminal, civil and/or administrative action.',8,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'sessions were provided as indicated.',0,1,'L');

		 	$pdf->Ln(4);
			$pdf->SetFont('Arial','U',8);
			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'                                                                                   ' ,0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');			   
			$pdf->Cell(125,4,'                                                                                   ' ,0,1,'L');
		 
			$pdf->SetFont('Arial','',8);
			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'Provider Signature',0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'Signature of Parent/Guardian/Principal',0,1,'L');

			$pdf->SetXY(97, 24);
      		$pdf->Cell(80, 4, $saved_school_name,1,0,'L');
	


				$pdf->AddPage();
				$saved_school_id = $row['SchoolId'];
				$saved_school_name = $row['SchoolName'];

				$i = 0; 


			}

 			$i++;
		 	 


			$pdf->Cell(5,4,'',0,0,'C');			
	 
			$pdf->Cell(25,4,$row['ExtId'],1,0,'L');
			$pdf->Cell(40,4,$row['LastName'],1,0,'L');
			$pdf->Cell(30,4,$row['FirstName'],1,0,'L');
	 
			$pdf->Cell(15,4,$row['DOB'],1,0,'L');
			$pdf->Cell(15,4,$row['ServiceDate'],1,0,'L');
			$pdf->Cell(20,4,$row['StartTime'],1,0,'L');
			$pdf->Cell(20,4,$row['EndTime'],1,0,'L');
			$pdf->Cell(15,4,$row['Duration'],1,0,'C');
			$pdf->Cell(15,4,$row['SessionGrpSize'],1,0,'C');
			$pdf->Cell(20,4,$row['MandateDesc'],1,0,'L');

			$delivery_method_desc = 'Tele-Therapy';
			if ($row['SessionDeliveryModeId'] == 'I') {

				$delivery_method_desc = 'In-Person';

			}

			$pdf->Cell(25,4,$delivery_method_desc,1,1,'L');
	 

			if ($i > 23) {

			$pdf->SetFont('Arial','',8);

		 	$pdf->Ln(10);
			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'I hereby certify that I have provided related services on the dates for the duration indicated herein.',0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'By my signature I acknowledge that I have review this Related.',0,1,'L');

			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'I understand that when completed and filed, this form becomes a record of the Board of Education',0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'Service billing form and that, to be best of my knowlerge, these',0,1,'L');

			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'and that any material misreprecentation may subject me to criminal, civil and/or administrative action.',8,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'sessions were provided as indicated.',0,1,'L');

		 	$pdf->Ln(4);
			$pdf->SetFont('Arial','U',8);
			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'                                                                                   ' ,0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');			   
			$pdf->Cell(125,4,'                                                                                   ' ,0,1,'L');
		 
			$pdf->SetFont('Arial','',8);
			$pdf->Cell(5,4,'',0,0,'C');			
			$pdf->Cell(125,4,'Provider Signature',0,0,'L');
			$pdf->Cell(5,4,'',0,0,'C');				
			$pdf->Cell(80,4,'Signature of Parent/Guardian/Principal',0,1,'L');

			$pdf->SetXY(97, 24);
      		$pdf->Cell(80, 4, $saved_school_name,1,0,'L');


				$pdf->AddPage();
				$i = 0;

			}
  
	}	

 
	$pdf->SetFont('Arial','',8);

 	$pdf->Ln(10);
	$pdf->Cell(5,4,'',0,0,'C');			
	$pdf->Cell(125,4,'I hereby certify that I have provided related services on the dates for the duration indicated herein.',0,0,'L');
	$pdf->Cell(5,4,'',0,0,'C');				
	$pdf->Cell(80,4,'By my signature I acknowledge that I have review this Related.',0,1,'L');

	$pdf->Cell(5,4,'',0,0,'C');			
	$pdf->Cell(125,4,'I understand that when completed and filed, this form becomes a record of the Board of Education',0,0,'L');
	$pdf->Cell(5,4,'',0,0,'C');				
	$pdf->Cell(80,4,'Service billing form and that, to be best of my knowlerge, these',0,1,'L');

	$pdf->Cell(5,4,'',0,0,'C');			
	$pdf->Cell(125,4,'and that any material misreprecentation may subject me to criminal, civil and/or administrative action.',8,0,'L');
	$pdf->Cell(5,4,'',0,0,'C');				
	$pdf->Cell(80,4,'sessions were provided as indicated.',0,1,'L');

 	$pdf->Ln(4);
	$pdf->SetFont('Arial','U',8);
	$pdf->Cell(5,4,'',0,0,'C');			
	$pdf->Cell(125,4,'                                                                                   ' ,0,0,'L');
	$pdf->Cell(5,4,'',0,0,'C');			   
	$pdf->Cell(125,4,'                                                                                   ' ,0,1,'L');
 
	$pdf->SetFont('Arial','',8);
	$pdf->Cell(5,4,'',0,0,'C');			
	$pdf->Cell(125,4,'Provider Signature',0,0,'L');
	$pdf->Cell(5,4,'',0,0,'C');				
	$pdf->Cell(80,4,'Signature of Parent/Guardian/Principal',0,1,'L');

			$pdf->SetXY(97, 24);
      		$pdf->Cell(80, 4, $saved_school_name,1,0,'L');

	$pdf->Output();

	$connection->disconnect();




?>
