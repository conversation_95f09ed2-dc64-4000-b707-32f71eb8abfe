<?php 

  
  error_reporting(E_ALL);
  ini_set('display_errors', TRUE);
  ini_set('display_startup_errors', TRUE);


  require_once("db_GetSetData.php");

  $conn = getCon();

      $query1 ="SELECT 
        ApprovalEmail, ApprovalEmailPass
    FROM
        Company";

      $result1 =  mysqli_query($conn, $query1) or die
      ("Error in Selecting " . mysqli_error($conn));

    while ($row1 = $result1->fetch_assoc()) {

         $username = $row1['ApprovalEmail'];
         $password = $row1['ApprovalEmailPass'];


    } 


    setDisConn($conn);

  
   /* connect to gmail */
  $hostname = '{imap.gmail.com:993/imap/ssl}INBOX';
  $username = '<EMAIL>';
  $password = 'tyaffqbzbhgsfhbp';

/* try to connect */
$inbox = imap_open($hostname,$username,$password) or die('Cannot connect to Gmail: ' . imap_last_error());

/* grab emails */
//$emails = imap_search($inbox,'ALL');
$emails = imap_search($inbox,'UNSEEN');

//$search_string = ' BODY "CONFIRMED"';   
//$emails = imap_search($inbox, $search_string);




/* if emails are returned, cycle through each... */
if($emails) {
  
  /* begin output var */
  $output = '';
  
  /* put the newest emails on top */
  rsort($emails);
  
  /* for every email... */
  foreach($emails as $email_number) {
    
    /* get information specific to this email */
    //$overview = imap_fetch_overview($inbox,$email_number,0);
    //$message = imap_fetchbody($inbox,$email_number,2);

    try
    {


      $headers = imap_fetchheader($inbox, $email_number, FT_PREFETCHTEXT);
      $body = imap_body($inbox, $email_number);


    echo '$body: '.$body.'</br>'; 


    $message_body = imap_fetchtext($inbox,$email_number);
    //$message_body = imap_fetchbody($inbox,$email_number,2.1);
    $message_body = quoted_printable_decode($message_body);


    $message_plain = imap_fetchbody($inbox,$email_number,2);
    //$message_plain = quoted_printable_decode($message_plain);

 
 

    //=====

    $structure = imap_fetchstructure($inbox, $email_number);

/*
        if(isset($structure->parts) && is_array($structure->parts) && isset($structure->parts[1])) {
            $part = $structure->parts[1];
            $message_1 = imap_fetchbody($inbox,$email_number,2);

            if($part->encoding == 3) {
                $message_1 = imap_base64($message_1);
            } else if($part->encoding == 1) {
                $message_1 = imap_8bit($message_1);
            } else {
                $message_1 = imap_qprint($message_1);
            }
        }
*/
        
        $encoding  = $structure->parts[1]->encoding;
    echo '$encoding: '.$encoding.'</br>'; 
    if ($encoding == 3) {

      $message_plain = imap_base64($message_plain);
    } else {

      $message_plain = quoted_printable_decode($message_plain);

    }


    echo '$message_plain: '.$message_plain.'</br>'; 


    //=====

    //$message_structure = imap_fetchstructure($inbox, $email_number);
    //echo '$message_structure: '.$message_structure.'</br>'; 

        //$confirmed_count = substr_count($message_body,"CONFIRMED");
    $message_body_upper = strtoupper($message_plain);
    $confirmed_count = substr_count($message_body_upper,"CONFIRM");



        $embeded_string = strstr($message_plain, '#d');
        echo '$embeded_string: '.$embeded_string.'</br>'; 

         
        $MandateId = get_string_between($embeded_string, '#d', 'd$');
        $Month = get_string_between($embeded_string, '#m', 'm$');
        //$Year = get_string_between($embeded_string, '#y', 'y$');
        $Year = '2020';




        $FormFromDate = $Year.'-'.$Month.'-01';
        //$FormFromDate = '2020-03-01';

        echo ' Month: '.$Month.' FromDate: '.$FormFromDate.'</br>'; 


        //=========
    $date = new DateTime($FormFromDate);
    $date->modify('last day of this month');
    $FormToDate =  $date->format('Y-m-d');

        //=========
        echo '$confirmed_count: '.$confirmed_count.'</br>'; 
   
 
      }   
      catch (Exception $e) {
        
      $confirmed_count = 0;

        echo 'Caught exception: ',  $e->getMessage(), "\n";
    }  


        $status =  imap_setflag_full($inbox, $email_number, "\\Flagged");
        echo '$status: '.$status.'</br>';

        if ($confirmed_count > 1) {




//         /* Generate Name for email copy uploaded file 
//        =============================================*/
//       $new_file_name =  generateRandomString();
//       $new_file_path =  '../em/'.$new_file_name.'.eml';
//       $db_new_file_name =  $new_file_name.'.eml';


 



      

// // Procoss RSA 7a Mandate - Start
// //============================



//         $conn = getCon();

//         $query ="UPDATE SchRsa7aFormSignatures a, 
//                         SchStudentMandates b,
//                         SchStudents c
//                SET  a.StatusId = '4',
//                 a.ParentSignatureName =  concat(c.GuardianFirstName,' ',c.GuardianLastName),  
//                 a.ParentApprovalEmailFileName =   '{$db_new_file_name}',      
//                 a.ParentSignatureTimeStamp = NOW()

//             WHERE a.MandateId =   '{$MandateId}'
//             AND   a.FromDate = '{$FormFromDate}'
//             AND   a.MandateId = b.Id 
//             AND   b.StudentId = c.Id ";




//         $result =  mysqli_query($conn, $query) or die
//         ("Error in Selecting " . mysqli_error($conn));

//         setDisConn($conn);


        
//         file_put_contents($new_file_path, $headers . "\n" . $body);


 

        }



  }
  
  //echo $output;

} 

  /* close the connection */
  imap_close($inbox);
   
  function get_string_between($string, $start, $end){
      $string = ' ' . $string;
      $ini = strpos($string, $start);
      if ($ini == 0) return '';
      $ini += strlen($start);
      $len = strpos($string, $end, $ini) - $ini;
      return substr($string, $ini, $len);
  }


  function generateRandomString($length = 15) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
      $randomString .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $randomString;
  } 

 

?>

