
<?php 
	


require_once("db_GetSetData.php");

	$conn = getCon();

 	$StudentId = $_GET['StudentId'];


$query ="SELECT Id as id, 
                Id,
				ExtId,
				SearchId,
				COALESCE(SchoolId,'') as SchoolId,
				StatusId,
				DateOfBirth,
				FirstName,
				LastName,
				CONCAT( trim( LastName) , ', ', trim(FirstName)) as StudentName,
				MiddleInitial,
				StreetAddress1,
				StreetAddress2,
				City ,
				State ,
				ZipCode ,
				COALESCE(MobilePhone,'') as MobilePhone,
				COALESCE(HomePhone,'') as HomePhone,
				COALESCE(GuardianFirstName,'') as GuardianFirstName,
				COALESCE(GuardianLastName,'') as GuardianLastName,
				COALESCE(GuardianPhone,'') as GuardianPhone,
				COALESCE(GuardianEmail,'') as GuardianEmail,
				
				MedicalNeeds,
				Comments,
				ReportGroupId,
				UserId
				
			FROM SchStudents
		  where Id = '{$StudentId}' "; 
	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;

/*

	require "ewDataHandler.php";  
	  
	$rcr_transaction = new dataHandler(); 

	$StudentId = $_GET['StudentId'];

	$result = $rcr_transaction->getSelSchStudent($StudentId);


	$rcr_transaction->disconnectDB (); 

	echo  "{ success: true,  data: ".json_encode($result)."}";
*/
  
?>
