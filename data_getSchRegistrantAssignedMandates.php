<?php 

 require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$FromDate = $_GET['FromDate'];  
	$ToDate = $_GET['ToDate'];  
	$FormTypeId = $_GET['FormTypeId'];  



  
	// $query = "	SELECT  a.Id as MandateId,
	// 					a.ServiceTypeId, 
	// 				   	c.ServiceTypeDesc,	
	// 			       	a.SessionLength,
	// 			       	a.SessionGrpSize,
	// 			       	a.SessionFrequency,
	// 			       	a.PlaceOfService,
	// 			       	d.SchoolName,
	// 			       	a.StudentId,
				       	
	// 			       	COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
	// 			       			WHERE a.Id = f.MandateId
	// 			       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  LIMIT 1 
	// 			       	),'0') as Rsa7aFormSignatureStatus,

				       	
	// 			       	COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
	// 			       			WHERE a.Id = f.MandateId
	// 			       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  LIMIT 1 
	// 			       	),'0') as Rsa7aFormId,

	// 			       	COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
	// 			       			WHERE a.Id = f.MandateId
	// 			       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  LIMIT 1 
	// 			       	),'') as ParentApprovalEmailFileName,
				       	
	// 			       	COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
	// 			       			WHERE a.Id = f.MandateId
	// 			       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  LIMIT 1 
	// 			       	),'') as Comments,

	// 			       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
	// 			       	(SELECT COUNT(*) FROM WeeklyServices d 
	// 			       	  	WHERE d.MandateId = a.Id 
	// 			       	  	AND d.ScheduleStatusId > 5
	// 			       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

	// 					) as NumbeOfSessions,
	// 					COALESCE(GuardianEmail,'') as GuardianEmail

	// 			FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
	// 			WHERE a.RegistrantId = '{$RegistrantId}'  
	// 		/*    AND a.MandateTypeId != '2' */
	// 			AND b.StatusId = '1'
	// 			AND a.StudentId = b.Id
	// 			AND a.ServiceTypeId = c.Id
	// 			AND a.Schoolid = d.Id
				
	// 			AND EXISTS ( SELECT 1 FROM WeeklyServices d 
	// 			       	  	WHERE d.MandateId = a.Id 
	// 			       	  	AND d.ScheduleStatusId > 5
	// 			       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

	// 			)
                

 //                Order By b.LastName, b.FirstName ";


	if ($FormTypeId == '1') { // In-Person

		$SessionDeliveryModeId = 'I';

		$query = "	SELECT  a.Id as MandateId,
							a.ServiceTypeId, 
						   	c.ServiceTypeDesc,	
					       	a.SessionLength,
					       	a.SessionGrpSize,
					       	a.SessionFrequency,
					       	a.PlaceOfService,
					       	d.SchoolName,
					       	a.StudentId,
					       	COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  
					       			AND   f.FormTypeId = '1'
					       			LIMIT 1 
					       	),'0') as Rsa7aFormSignatureStatus,

					       	
					       	COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'
					       			AND   f.FormTypeId = '1'
					       			  LIMIT 1 
					       	),'0') as Rsa7aFormId,

					       	COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  
					       			AND   f.FormTypeId = '1'
					       			LIMIT 1 
					       	),'') as ParentApprovalEmailFileName,
					       	
					       	COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}' 
					       			AND   f.FormTypeId = '1'
					       			 LIMIT 1 
					       	),'') as Comments,

					       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
					       	(SELECT COUNT(*) FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId > 5
					       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

							) as NumbeOfSessions,
							COALESCE(d.Email,'') as GuardianEmail
							
					FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
					WHERE a.RegistrantId = '{$RegistrantId}'  
					AND b.StatusId = '1'
					AND a.StudentId = b.Id
					AND a.ServiceTypeId = c.Id
					AND a.Schoolid = d.Id
					
					AND EXISTS ( SELECT 1 FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId > 5
					       	  	AND  find_in_set (d.SessionDeliveryModeId,'{$SessionDeliveryModeId}')
					       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

					)
	                

	                Order By b.LastName, b.FirstName ";


	} else { 	

		$SessionDeliveryModeId = 'V,A';	

		$query = "	SELECT  a.Id as MandateId,
							a.ServiceTypeId, 
						   	c.ServiceTypeDesc,	
					       	a.SessionLength,
					       	a.SessionGrpSize,
					       	a.SessionFrequency,
					       	a.PlaceOfService,
					       	d.SchoolName,
					       	a.StudentId,
					       	COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}' 
					       			AND   f.FormTypeId = '2'

					       			 LIMIT 1 
					       	),'0') as Rsa7aFormSignatureStatus,

					       	
					       	COALESCE((SELECT f.Id FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}' 
					       			AND   f.FormTypeId = '2'
					       			 LIMIT 1 
					       	),'0') as Rsa7aFormId,

					       	COALESCE((SELECT f.ParentApprovalEmailFileName FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}'  
					       			AND   f.FormTypeId = '2'
					       			LIMIT 1 
					       	),'') as ParentApprovalEmailFileName,
					       	
					       	COALESCE((SELECT f.Comments FROM SchRsa7aFormSignatures f
					       			WHERE a.Id = f.MandateId
					       			AND   f.FromDate =  '{$FromDate}' and f.ToDate = '{$ToDate}' 
					       			AND   f.FormTypeId = '2'
					       			 LIMIT 1 
					       	),'') as Comments,

					       	CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
					       	(SELECT COUNT(*) FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId > 5
					       	  	AND  find_in_set (d.SessionDeliveryModeId,'{$SessionDeliveryModeId}')

					       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

							) as NumbeOfSessions,
							COALESCE(GuardianEmail,'') as GuardianEmail

							
					FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
					WHERE a.RegistrantId = '{$RegistrantId}'  
					AND b.StatusId = '1'
					AND a.StudentId = b.Id
					AND a.ServiceTypeId = c.Id
					AND a.Schoolid = d.Id
					
					AND EXISTS ( SELECT 1 FROM WeeklyServices d 
					       	  	WHERE d.MandateId = a.Id 
					       	  	AND d.ScheduleStatusId > 5
					       	  	AND  find_in_set (d.SessionDeliveryModeId,'{$SessionDeliveryModeId}')
					       	  	AND d.ServiceDate between '{$FromDate}' and '{$ToDate}' 

					)
	                

	                Order By b.LastName, b.FirstName ";		

	}
	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
	


 