
/*=========================================*/

DELIMITER $$

DROP PROCEDURE IF EXISTS proc_getPayrollPeriodUnPaidHours$$

CREATE PROCEDURE proc_getPayrollPeriodUnPaidHours (p_from_date date, p_to_date date)  

BEGIN


   
   
   /*============================================*/
   
   create temporary table tmp

SELECT   distinct 
                      /*CONCAT( trim( c.FirstName) , ' ', trim(c.LastName)) as RegistrantName, */
                      c.FirstName,
                      c.LastName,
                      a.RegistrantId,
                      c.PayrollId,
                      DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                      a.ServiceDate AS ServiceDateUnf,
                      a.StartTime,
                      a.EndTime,
                      'Therapy' as SessionTypeDesc,
                      TherapyPayRate as  PayRate,
                      a.TotalHours 
                    FROM  WeeklyServices a, 
                           Registrants c  
                       WHERE   a.ServiceDate between p_from_date AND p_to_date 
                        AND   a.ScheduleStatusId > 5
                          AND   a.RegistrantId = c.Id 
                        AND   a.PaidFL = '0' 
                         AND   a.SessionTypeId ='0'  
      union             
 SELECT  
                      /*CONCAT( trim( c.FirstName) , ' ', trim(c.LastName)) as RegistrantName, */
                      c.FirstName,
                      c.LastName,
                      a.RegistrantId,
                      c.PayrollId,
                      DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                      a.ServiceDate AS ServiceDateUnf,
                      a.StartTime,
                      a.EndTime,
                      'Evaluation' as SessionTypeDesc,
                      EvalPayRate as  PayRate,
                      a.TotalHours  
                    FROM  WeeklyServices a, 
                           Registrants c  
                        WHERE   a.ServiceDate between p_from_date AND p_to_date  
                        AND   a.ScheduleStatusId > 5
                          AND   a.RegistrantId = c.Id 
                        AND   a.PaidFL = '0' 
                         AND   a.SessionTypeId ='1'  
            union       
 SELECT 
                      /*CONCAT( trim( c.FirstName) , ' ', trim(c.LastName)) as RegistrantName, */
                      c.FirstName,
                      c.LastName,
                      a.RegistrantId,
                      c.PayrollId,
                      DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                      a.ServiceDate AS ServiceDateUnf,
                      a.StartTime,
                      a.EndTime,
                      
                      'Supervision' as SessionTypeDesc,
                      SupervisionPayRate as  PayRate,
                      a.TotalHours 
                    FROM  WeeklyServices a, 
                           Registrants c  
                        WHERE   a.ServiceDate between p_from_date AND p_to_date 
                        AND   a.ScheduleStatusId > 5
                          AND   a.RegistrantId = c.Id 
                        AND   a.PaidFL = '0' 
                         AND   a.SessionTypeId ='2'  ;
 
   /*===================================*/
   SELECT RegistrantId,
          CONCAT( trim( FirstName) , ' ', trim(LastName)) as RegistrantName,  
          ServiceDate,
          ServiceDateUnf,
          SessionTypeDesc,
          PayRate,
          PayrollId,
          SUM(TotalHours) as TotalHours

    FROM tmp 
   GROUP BY RegistrantId,
            LastName,
            FirstName, 
            ServiceDate,
            ServiceDateUnf,
            SessionTypeDesc,
            PayRate,
            PayrollId
   ORDER BY LastName, FirstName, ServiceDateUnf,   SessionTypeDesc      
              ;
               

   DROP TEMPORARY TABLE IF  EXISTS tmp;
   
END $$

DELIMITER ; 