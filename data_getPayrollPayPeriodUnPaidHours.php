<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


    $query = "	SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
                      group_concat( CONCAT(b.FirstName, ' ', b.LastName)    SEPARATOR '; ' ) as StudentName,
                      group_concat( a.Id    SEPARATOR ',' ) as SessionSchedulesList,
                      
                      a.SessionGrpSize,
                      a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      SchoolName 
                  FROM  WeeklyServices a, SchStudents b, ScheduleStatuses g, SchSchools d, SchStudentMandates e 
                        WHERE   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   b.Id = a.StudentId
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SchoolId = d.Id
                        AND   a.MandateId = e.Id 
                        AND   a.SessionTypeId = 0
                   GROUP BY ServiceDateSort, a.StartTime, a.EndTime, a.TotalHours

           union       
   SELECT    DATE_FORMAT( a.ServiceDate, '%m-%d-%Y' ) AS ServiceDate,     
                        a.ServiceDate AS ServiceDateSort,
                                  DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,  
                      DATE_FORMAT( a.EndTime, '%l:%i %p' ) EndTime,
                      FORMAT((a.TotalHours * 60), 0) as TotalHours,
                      
                      case a.SessionTypeId
                      when '1' then 'Evaluation' 
                      when '2' then 'Supervision'
                      end as StudentName,
                      
                      
                      a.Id SessionSchedulesList,
                      
                      '' as SessionGrpSize,
                       a.RegistrantId,
                      (SELECT a.ScheduleStatusId FROM WeeklyServices c 
                                                    where a.Id = c.id LIMIT 1  
                                            ) as ScheduleStatusId,
                                            ScheduleStatusDesc,
                      TextColor,
                      BackgroundColor,
                      '' as SchoolName 
                  FROM  WeeklyServices a,   ScheduleStatuses g  
                        WHERE   a.ServiceDate between '{$FromDate}' AND '{$ToDate}' 
                        AND   a.ScheduleStatusId > 5
                        AND   a.ScheduleStatusId = g.Id
                        AND   a.SessionTypeId > 0

                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

