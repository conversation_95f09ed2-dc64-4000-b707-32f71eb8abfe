    DELIMITER $$

    DROP PROCEDURE IF EXISTS proc_getSchRegistrantApproveRsa7aFormsHeaders$$

    CREATE PROCEDURE proc_getSchRegistrantApproveRsa7aFormsHeaders (IN  p_registrant_id INT, 
                                                                    p_from_date DATE,
                                                                    p_to_date DATE 
                                                                )     

                                        

    BEGIN

      
      /* In_Person Forms*/  
      /*======================*/
      create temporary table tmp

 
      SELECT       a.Id as MandateId,
            a.ServiceTypeId, 
              c.ServiceTypeDesc,  
                a.SessionLength,
                a.SessionGrpSize,
                a.SessionFrequency,
                a.PlaceOfService,
                d.SchoolName,
                a.StudentId,

                -- COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
                --     WHERE a.Id = f.MandateId
                --     AND   f.FromDate =  p_from_date and f.ToDate = p_to_date   
                -- ),'0') as Rsa7aFormSignatureStatus,

                '0' as Rsa7aFormSignatureStatus,   
                COALESCE(d.Email,'') as GuardianEmail,

                CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                (SELECT COUNT(*) FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId > 6
                     AND  find_in_set (d.SessionDeliveryModeId,'I')
                    AND d.ServiceDate between p_from_date and p_to_date 

            ) as NumbeOfSessions,
            'In-Person      ' as FormType

        FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
        WHERE a.RegistrantId = p_registrant_id  
        AND b.StatusId = '1'
        AND a.StudentId = b.Id
        AND a.ServiceTypeId = c.Id
        AND a.Schoolid = d.Id
        AND b.StudentTypeId != '2' 
        AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId > 6
                    AND  find_in_set (d.SessionDeliveryModeId,'I')
                    AND d.ServiceDate between p_from_date and p_to_date 

        )
        AND NOT EXISTS ( SELECT 1 FROM SchRsa7aFormSignatures h 
                    WHERE h.MandateId = a.Id 
                    AND h.FromDate = p_from_date
                    AND h.ToDate = p_to_date 
                    AND h.FormTypeId = '1' 

        ) ;


      /* Tele_Therapy Forms*/  
      /*======================*/
      insert into tmp
       
       SELECT   a.Id as MandateId,
                a.ServiceTypeId, 
                c.ServiceTypeDesc,  
                a.SessionLength,
                a.SessionGrpSize,
                a.SessionFrequency,
                a.PlaceOfService,
                d.SchoolName,
                a.StudentId,
                -- COALESCE((SELECT f.StatusId FROM SchRsa7aFormSignatures f
                --     WHERE a.Id = f.MandateId
                --     AND   f.FromDate =  p_from_date and f.ToDate = p_to_date   
                -- ),'0') as Rsa7aFormSignatureStatus,

                '0' as Rsa7aFormSignatureStatus,               
                COALESCE(GuardianEmail,'') as GuardianEmail,

                CONCAT(b.LastName, ', ', b.FirstName) as StudentName,
                (SELECT COUNT(*) FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId > 6
                    AND  find_in_set (d.SessionDeliveryModeId,'V,A')
                    AND d.ServiceDate between p_from_date and p_to_date 

            ) as NumbeOfSessions,
            'Tele-Therapy' as FormType

        FROM SchStudentMandates a, SchStudents b, SchServiceTypes c, SchSchools d
        WHERE a.RegistrantId = p_registrant_id  
      /*  AND a.StatusId = '1' */
        AND b.StatusId = '1'
        AND a.StudentId = b.Id
        AND a.ServiceTypeId = c.Id
        AND a.Schoolid = d.Id
          AND b.StudentTypeId != '2' 
        AND EXISTS ( SELECT 1 FROM WeeklyServices d 
                    WHERE d.MandateId = a.Id 
                    AND d.ScheduleStatusId > 6
                    AND  find_in_set (d.SessionDeliveryModeId,'V,A')
                    AND d.ServiceDate between p_from_date and p_to_date 

        )
        AND NOT EXISTS ( SELECT 1 FROM SchRsa7aFormSignatures h 
                    WHERE h.MandateId = a.Id 
                    AND h.FromDate = p_from_date
                    AND h.ToDate = p_to_date 
                     AND h.FormTypeId = '2' 

        ) ;     
     

      SELECT * FROM tmp
      order by StudentName  
      ; 


      drop temporary table if exists tmp;
       
      
    END $$

    DELIMITER ;   
     