<?php  

/*

    error_reporting(E_ALL);  
    ini_set('display_errors', TRUE);
    ini_set('display_startup_errors', TRUE);
*/
 
 
 $dir = "../pr/";


$files = scandir($dir, 0);
for($i = 2; $i < count($files); $i++) {


    $FileName = $files[$i];
  
  		echo 'FileName: '.$FileName.'</br>'; 



         $url_qb = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_getUploadParentsInfo.php";
         


         

        $fields_qb = array('FileName' => $FileName);
                          

        $curl = curl_init();
       

         
        curl_setopt($curl, CURLOPT_URL, $url_qb);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $fields_qb); 
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false

        // execute and return string (this should be an empty string '')
        $str = curl_exec($curl);

        //echo '$str :'.$str;
        
        curl_close($curl);
     


} 
 

     
?>