
<?php 
	

	require_once("db_GetSetData.php");

	$StudentId = $_GET['StudentId'];


	$conn = getCon();

    $query = "	 SELECT 	a.Id as id,
							a.Id as MandateId,
							StudentId, 
							CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
							a.StatusId,
							CASE a.SchoolId 
								WHEN '0' THEN ''
							ELSE a.SchoolId
							END AS SchoolId,

							(SELECT SchoolName from SchSchools c
							   WHERE a.SchoolId = c.Id ) as SchoolName,  
							ServiceTypeDesc,
							ServiceTypeId,
							a.StatusId , 
							SECMandateStatus,
							DATE_FORMAT( StartDate, '%m-%d-%Y' ) as StartDate,
							DATE_FORMAT( EndDate, '%m-%d-%Y' ) as EndDate,
							CASE a.RegistrantId 
								WHEN '0' THEN ''
							ELSE a.RegistrantId
							END AS RegistrantId,
							

							CallInDate,
							DOEAssignmentDate,
							PlaceOfService,

							SECMandateStatus,
							DATE_FORMAT( DOEAssignmentDate, '%m-%d-%Y' ) as DOEAssignmentDate,
							CONCAT( SessionFrequency , ' X ', SessionLength , ' X ', SessionGrpSize ) AS MandateDesc,
							Language,
							CONCAT( trim( RegistrantExtFirstName) , ' ', trim(RegistrantExtLastName)) as RegistrantName,
							CONCAT( trim( e.FirstName ) , ' ', trim( e.LastName ) ) AS UserName,
							DOESchoolName,

							COALESCE(a.SpecialPayRate,'') as  	SpecialPayRate,

							a.UserId , 
							a.TransDate
			
							FROM 	SchStudentMandates a, 
									SchStudents b, 
									SchServiceTypes f,
								Users e
								WHERE a.StudentId = '{$StudentId}'
								AND a.StudentId = b.Id	
								AND a.ServiceTypeId = f.Id
								AND a.UserId = e.UserId
							ORDER BY ServiceTypeId 

			"; 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;	  

	// require "ewDataHandler.php";  
	  
	// $rcr_transaction = new dataHandler(); 

	// $StudentId = $_GET['StudentId'];

	// $result = $rcr_transaction->getSchStudentMandatesList($StudentId);


	// $rcr_transaction->disconnectDB (); 

	// echo  "{ success: true,  data: ".json_encode($result)."}";

  
?>
