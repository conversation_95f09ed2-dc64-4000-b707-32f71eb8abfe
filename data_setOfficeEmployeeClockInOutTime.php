<?php 
 
/*	
	error_reporting(E_ALL);
	ini_set('display_errors', TRUE);
	ini_set('display_startup_errors', TRUE);
*/

	require_once("db_GetSetData.php");

	$conn = getCon();

	 
	$OfficeEmployeeId = $_POST['OfficeEmployeeId'];
	$PayrollRecordId = $_POST['PayrollRecordId'];
	$WorkDate = $_POST['WorkDate'];

	$ClockUpdateAction =  $_POST['ClockUpdateAction'];
	$PayrollRecordId = $_POST['PayrollRecordId'];
	$ClockTime = $_POST['ClockTime'];
	$UserId = $_POST['UserId'];

  
 	if ($ClockUpdateAction == 'Clock-In') {

	    $query = "INSERT INTO OfficeEmployeesPayrollRecords 	
	    		  (
					OfficeEmployeeId,
					PayrollTypeId,
					PayrollStatusId,
					WorkDate,
					Payrollweek,
					StartTime,
					ClockInTime,
					UserId,
					TransDate
	    		  )			 
				  VALUES 
				  (
				  '{$OfficeEmployeeId}',
				  '1',
				  '1',
				  '{$WorkDate}',
				   (DATE_ADD(curdate(),INTERVAL IF(WEEKDAY(NOW())>=5,
                             (6-WEEKDAY(NOW())),
                             (5-WEEKDAY(NOW()))) DAY)),

                   SEC_TO_TIME(FLOOR((TIME_TO_SEC('{$ClockTime}')+450)/900)*900),
                   '{$ClockTime}',
                   '{$UserId}',
                   NOW()          
				  ) ";

 

 	} else {

	    $query = "UPDATE OfficeEmployeesPayrollRecords 			 
				   SET ClockOutTime = '{$ClockTime}',
				       EndTime = SEC_TO_TIME(FLOOR((TIME_TO_SEC('{$ClockTime}')+450)/900)*900),  
				       UserId = '{$UserId}',
				       TransDate = NOW()
				 WHERE Id =  '{$PayrollRecordId}'  	";


 	}	
 	   
 	

	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	/*===============*/


	if ($ClockUpdateAction == 'Clock-Out') {

		$conn = getCon();
		/*
		    $query = "	   UPDATE OfficeEmployeesPayrollRecords 			 
					   SET TotalHours =  (
					   	  case 
	           when EndTime > StartTime Then time_to_sec(timediff(EndTime, StartTime))/3600 
	           else time_to_sec(timediff(addtime(EndTime, '24:00:00'), StartTime))/3600
			   end 	

					   )
					 WHERE Id =  '{$PayrollRecordId}'  	";
	   	*/
		    $query = "	   UPDATE OfficeEmployeesPayrollRecords 			 
					   SET TotalHours =  time_to_sec(timediff(EndTime, StartTime))/3600
 					   WHERE Id =  '{$PayrollRecordId}'  	";


		$ret =  setData ($conn, $query);   			
		setDisConn($conn);

    }

 	echo $query; 

?>
