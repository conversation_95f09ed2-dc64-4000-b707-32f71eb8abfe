<?php 

	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$RegistrantId = $_POST['RegistrantId'];
	$ServiceDate = $_POST['ServiceDate'];
    $PayrollWeek = $_POST['PayrollWeek'];
	$WeekDay = $_POST['WeekDay'];
	$StartTime = $_POST['StartTime'];
	$EndTime = $_POST['EndTime'];
	$TotalHours = $_POST['TotalHours'];
	$SupervisedCFId = $_POST['SupervisedCFId'];
	$UserId = $_POST['UserId'];


 			
	$query ="INSERT INTO WeeklyServices
							                  (
												PayrollWeek,
												ScheduleStatusId,
												ServiceDate,	 
												StartTime, 
												EndTime, 		
												TotalHours , 
												WeekDay ,
												RegistrantId, 
												SupervisedCFId,
												SessionTypeId,
												UserId,
												TransDate )	
												
						    VALUES  (


										'{$PayrollWeek}',
										'7',
										'{$ServiceDate}',	 
										'{$StartTime}',	 
                                        '{$EndTime}', 		
                                        '{$TotalHours}', 
										'{$WeekDay}',
                                        '{$RegistrantId}',
										'{$SupervisedCFId}',
										'2',
 									 	'{$UserId}',
                                        NOW()  


						    )
						    ";



	$ret =  setData ($conn, $query);   			
	setDisConn($conn);
	echo $ret;
	//echo $query;


?>



