<?php

  
$res = array(
    (object)array(
        'StartDate' => date('Y-m-d', strtotime("first day of -0 month")),
        'EndDate' => date('Y-m-15', strtotime("last day of -0 month")),
 		'id' => date('m-d-Y', strtotime("first day of -0 month")).' to '.date('m-15-Y', strtotime("last day of -0 month"))
    ),
 

    (object)array(
        'StartDate' => date('Y-m-16', strtotime("first day of -0 month")),
        'EndDate' => date('Y-m-d', strtotime("last day of -0 month")),
 		'id' => date('m-16-Y', strtotime("last day of -0 month")).' to '.date('m-d-Y', strtotime("last day of -0 month"))
    ),


    (object)array(
        'StartDate' => date('Y-m-d', strtotime("first day of -1 month")),
        'EndDate' => date('Y-m-15', strtotime("last day of -1 month")),
 		'id' => date('m-d-Y', strtotime("first day of -1 month")).' to '.date('m-15-Y', strtotime("last day of -1 month"))
    ),
 

    (object)array(
        'StartDate' => date('Y-m-16', strtotime("first day of -1 month")),
        'EndDate' => date('Y-m-d', strtotime("last day of -1 month")),
 		'id' => date('m-16-Y', strtotime("last day of -1 month")).' to '.date('m-d-Y', strtotime("last day of -1 month"))
    ),

    (object)array(
        'StartDate' => date('Y-m-d', strtotime("first day of -2 month")),
        'EndDate' => date('Y-m-15', strtotime("last day of -2 month")),
 		'id' => date('m-d-Y', strtotime("first day of -2 month")).' to '.date('m-15-Y', strtotime("last day of -2 month"))
    ),
 

    (object)array(
        'StartDate' => date('Y-m-16', strtotime("first day of -2 month")),
        'EndDate' => date('Y-m-d', strtotime("last day of -2 month")),
 		'id' => date('m-16-Y', strtotime("last day of -2 month")).' to '.date('m-d-Y', strtotime("last day of -2 month"))
    ),



/*     
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 5 day')),
        'dow' => 'mon',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 5 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 4 day')),
        'dow' => 'tue',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 4 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 3 day')),
        'dow' => 'wed',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 3 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 2 day')),
        'dow' => 'thu',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 2 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek.' - 1 day')),
        'dow' => 'fri',
		'id' => date('m-d-Y',strtotime($PayrollWeek.' - 1 day'))
    ),
    (object)array(
        'wkdate' => date('m-d-Y',strtotime($PayrollWeek)),
        'dow' => 'sat',
		'id' => date('m-d-Y',strtotime($PayrollWeek))

    ),
*/
     
);

echo  "{ success: true,  data: ".json_encode($res)."}";
 

/* 

echo 'Month 1st: '.date('Y-m-d', strtotime("first day of -0 month")).'</br>'; 
echo 'Month Middle: '.date('Y-15-d', strtotime("first day of -0 month")).'</br>'; 

echo 'Month Last: '.date('Y-m-d', strtotime("last day of -0 month")).'</br>';


echo '-1 Month 1st: '.date('Y-m-d', strtotime("first day of -1 month")).'</br>'; 
echo '-1 Month Last: '.date('Y-m-d', strtotime("last day of -1 month")).'</br>';


echo '-2 Month 1st: '.date('Y-m-d', strtotime("first day of -2 month")).'</br>'; 
echo '-2 Month Last: '.date('Y-m-d', strtotime("last day of -2 month")).'</br>';
*/ 
 

?>