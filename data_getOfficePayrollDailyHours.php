<?php 

	require_once("db_GetSetData.php");

	$WorkDate = $_GET['WorkDate'];  
	$OfficeEmployeeId = $_GET['OfficeEmployeeId'];  


	$conn = getCon();

    $query = "	 SELECT Id as PayrollRecordId,
    					PayrollTypeId,
    					Workdate as WorkdateUnf,
    					DATE_FORMAT(  WorkDate, '%m-%d-%Y' ) AS WorkDate, 
						DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						StartTime as StartTimeUnf,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						EndTime as EndTimeUnf,
						TotalHours  

				 from    OfficeEmployeesPayrollRecords  
				    WHERE OfficeEmployeeId = '{$OfficeEmployeeId}'  
				    AND WorkDate = '{$WorkDate}'

			"; 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;	  

?>
