<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


    $query = "	SELECT a.Id as PayrollRecordId,
              PayrollTypeId,
              case PayrollTypeId 
               when '1' then 'Regular'
               else 'Time Off'
              end as PayrollTypeDesc, 
              PaidFl,
              Workdate as WorkdateUnf,
              DATE_FORMAT(  WorkDate, '%m-%d-%Y' ) AS WorkDate, 
            DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
            StartTime as StartTimeUnf,
            DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
            EndTime as EndTimeUnf,
            TotalHours,
            concat(FirstName, ' ',  LastName) as EmployeeName

         from    OfficeEmployeesPayrollRecords a, OfficeEmployees b 
            WHERE WorkDate between '{$FromDate}' and '{$ToDate}'
            and  OfficeEmployeeId = b.Id            
                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

