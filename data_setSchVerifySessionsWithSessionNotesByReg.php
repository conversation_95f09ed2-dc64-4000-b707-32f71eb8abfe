<?php  

	ini_set("memory_limit","-1");


	/** Include path **/
	//set_include_path(get_include_path() . PATH_SEPARATOR . '../../Classes/');


	/** PHPExcel_IOFactory */
	//include 'PHPExcel/IOFactory.php';
	
    require_once('DB.php');
	include('db_login.php');
	require('fpdf/fpdf.php'); 
	include('../../phpexcel-1-8/Classes/PHPExcel/IOFactory.php');


	$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysqli://$db_username:$db_password@$db_host/$db_database");
    }


    $RegistrantId = $_GET['RegistrantId'];
    $RegistrantNameDisp = $_GET['RegistrantName']; 
    $StoredFileName = $_GET['StoredFileName'];
    $FileExt = $_GET['FileExt'];

 
    if ($FileExt == 'xlsx') {

		$inputFileType = 'Excel2007';
	 
	} else {
 
		$inputFileType = 'Excel5';

 	}	
	 

	$sheetname = 'Sheet1';


	$inputFileName = '../hr/'.$StoredFileName.'.'.$FileExt;
	//$inputFileName = '../hr/PVwa51LLIu6K19Vv1JhT.xls';

 
	/**  Create a new Reader of the type defined in $inputFileType  **/
	$objReader = PHPExcel_IOFactory::createReader($inputFileType);
	/**  Load $inputFileName to a PHPExcel Object  **/
	
	$objReader->setLoadSheetsOnly($sheetname);
	$objReader->setReadDataOnly(true);


	$objPHPExcel = $objReader->load($inputFileName);


	$sheetData = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
	
	 
	
	$i = 0;	
	$linecount = 0;	
    $matched_sessions = 0;
 

	class PDF extends FPDF
	{
	//Page header
	function Header()
	{	
		$this->SetLeftMargin(5);
		$this->SetRightMargin(5);

		$this->Ln(4);

		$this->SetFont('Arial','B',14);

		$this->Cell(0,5,'Session Notes Verification Results',0,1,'C');

		//$this->Ln(10);
		//$this->SetFont('Arial','',11);
		
	}

	//Page footer
	function Footer()
	{
		//Position at 1.5 cm from bottom
		$this->SetY(-15);
		//Arial italic 8
		
		$this->SetFont('Arial','B',11);
		//$this->SetX(5);
		$this->Cell(0,5,'_______________________________________________________________________________________',0,1,'C');

		$this->SetFont('Arial','I',8);
		//Page number
		$this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
	}
	}




	//Instanciation of inherited class
	$pdf=new PDF();
	$pdf->AliasNbPages();
	$pdf->AddPage();
	//$pdf->SetFont('Times','',12);
	$pdf->SetFont('Arial','',10);
 


	foreach ($sheetData as &$row) { // read excel - start  

	 	
			 	
 
		// Extract Notes Date Info
		//=========================================== 
			$date_field_signature  = 'Services (as of';
			if  (strstr($row["A"], $date_field_signature)) {

				
				$upload_file_desc = $row["A"];


				getMonthStartEndDate($upload_file_desc);

				$pdf->SetFont('Arial','I',11);

				$pdf->Cell(0,5,'Session Notes Date: '.$row["A"],0,0,'C');


				$pdf->Ln(10);
				$pdf->SetFont('Arial','',11);



			}



		// Extract Session Info
		//=========================================== 

		 	if (($row["M"] == 'Service Provided') || ($row["M"] == 'Service provided - Make-up')) { // Sessions Section - Start  
				

				/*=== Student ID ======*/
				$student_ext_id = $row["A"];

				/*=== Student Last Name ======*/
				$student_last_name = $row["B"];

				/*=== Student First Name ======*/
				$student_first_name = $row["C"];

				$StudentNameDisp = $student_first_name.' '.$student_last_name; 

				/*=== Session Date ======*/

				//$service_date_x = $row["H"];
				//$service_date = date('Y-m-d', PHPExcel_Shared_Date::ExcelToPHP($service_date_x));	 		 	
				//$service_date_disp = date('m/d/Y', PHPExcel_Shared_Date::ExcelToPHP($service_date_x));	 		 	

				$service_date_x = PHPExcel_Shared_Date::ExcelToPHPObject($row["H"]);
 	 			$service_date = $service_date_x->format('Y-m-d');		 
 	 			$service_date_disp = $service_date_x->format('m/d/Y');		 
 

				/*=== Session Start Time ======*/

				//$start_time_x = $row["I"];
				//$start_time = date('H:i:s', PHPExcel_Shared_Date::ExcelToPHP($start_time_x));	 		 	
				//$start_time_frm = date('g:i A', PHPExcel_Shared_Date::ExcelToPHP($start_time_x));	 		 	
 
				$start_time_x = PHPExcel_Shared_Date::ExcelToPHPObject($row["I"]);
 	 			$start_time = $start_time_x->format('H:i:s');		 
 	 			$start_time_frm = $start_time_x->format('g:i A');		 

				/*=== Session End Time ======*/

				//$end_time_x = $row["J"];
				//$end_time = date('H:i:s', PHPExcel_Shared_Date::ExcelToPHP($end_time_x));	 		 	
				//$end_time_frm = date('g:i A', PHPExcel_Shared_Date::ExcelToPHP($end_time_x));	 		 	

				$end_time_x = PHPExcel_Shared_Date::ExcelToPHPObject($row["J"]);
 	 			$end_time = $end_time_x->format('H:i:s');		 
 	 			$end_time_frm = $end_time_x->format('g:i A');		 


				/*=== Service Type ======*/
				$service_type = $row["K"];


					/*=== Group Size ======*/

			 	if ($row["Q"]  == 'Individual' ) {
					$group_size = '1';		 		
			 	
			 	} else {

					$grp_desc = $row["Q"];	
					$group_size = preg_replace('/\D/', '', $grp_desc);
			 	 	 		

			 	}

			/* 	
			 	if ($student_ext_id == '233095348'){

				echo 'Student ID: '.$student_ext_id.' Name: '.$student_first_name.' '.$student_last_name.' Service Date: '.$service_date.' Start Time: '.$start_time_frm.' End Time: '.$end_time.' Group Size: '.$group_size.'</br>';

			 	}
			*/


				//================================================= 
				//  Check if Session Exists in eWeb for Registrant/Student  
				//================================================= 
			  	
				$query1 = "SELECT  	a.Id as ScheduleId,
									a.StartTime, 
									a.EndTime, 
									DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTimeFrm,
									DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTimeFrm,

									a.SessionGrpSize,
									CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as RegistrantName, 
									CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
									b.ExtId as StudentExtId

					FROM WeeklyServices a,
					     SchStudents b,
					     Registrants c
					WHERE c.Id = '{$RegistrantId}' 
					AND   a.RegistrantId = c.Id
					AND   b.ExtId = '{$student_ext_id}' 

					AND   a.StudentId = b.Id 
					AND   a.ServiceDate = '{$service_date}' 
					AND   a.ScheduleStatusId = 7  

		                ";

			        //echo '$query1: '.$query1.'</br>';
			$result1 = $connection->query ($query1);

		 	
			if (DB::isError($result1)){
						die("Could not query the database:<br />$query1 ".DB::errorMessage($result1));
			}			
	 	 	
			$session_exists =& $result1->numRows();			
	 		


			if ($session_exists > 0) { // session_exists - start





				while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {
						$schedule_id = $row1['ScheduleId'];
						
						$eweb_start_time = $row1['StartTime'];
						$eweb_end_time = $row1['EndTime'];
						
						$eweb_start_time_frm = $row1['StartTimeFrm'];
						$eweb_end_time_frm = $row1['EndTimeFrm'];

						$eweb_group_size = $row1['SessionGrpSize'];
						$registrant_name = $row1['RegistrantName'];
						$student_name = $row1['StudentName'];
						$student_ext_id = $row1['StudentExtId'];


				}  

				// Sessions to be skipped in "Found in eWeb, Missing in Notes" check 
				//================================================================ 
				if (!$skip_sessions_list) {

					$skip_sessions_list = $schedule_id;

				} else {
					$skip_sessions_list = $skip_sessions_list.','.$schedule_id;

				}	



			 
				if (($start_time == $eweb_start_time) && ($end_time == $eweb_end_time)  && ($group_size == $eweb_group_size )) { // Verified


					$query2 = " UPDATE WeeklyServices
									SET ScheduleStatusId = '8',
                                        UserId = '{$user_id}',
                                        TransDate = NOW()
   								WHERE Id = '{$schedule_id}'	 	
			                ";
		                
					$result2 = $connection->query ($query2);

	 	
					if (DB::isError($result2)){
								die("Could not query the database:<br />$query2 ".DB::errorMessage($result2));
					}			
				
					++$matched_sessions;

				} else { // Discrepancy Report




					$pdf->Ln(2);

		$pdf->Cell(0,5,'_____________________________________________________________________________',0,1,'L');

					$pdf->Cell(20,5,'Provider: ',0,0,'L');
					$pdf->Cell(40,5,$registrant_name,0,0,'L');
					$pdf->Cell(20,5,'Student: ',0,0,'L');
					$pdf->Cell(50,5,$student_name,0,0,'L');
					$pdf->Cell(15,5,'Date: ',0,0,'L');
					$pdf->Cell(30,5,$service_date_disp,0,1,'L');


					if ($start_time != $eweb_start_time) {


					$pdf->Cell(10,5,'',0,0,'L');
					$pdf->Cell(40,5,'(1)Start Time (S.N.): ',0,0,'L');
					$pdf->SetTextColor(220,50,50);
					$pdf->Cell(20,5,$start_time_frm,1,0,'L');
					$pdf->SetTextColor(0,0,0);
					$pdf->Cell(45,5,'Start Time (RSA7a): ',0,0,'L');
					$pdf->SetTextColor(0,0,255);
					$pdf->Cell(30,5,$eweb_start_time_frm,1,1,'L');
					$pdf->SetTextColor(0,0,0);

					}


					if ($end_time != $eweb_end_time) {

			 	///echo 'Student: '.$student_name.' StudentId: '.$student_ext_id.' Service Date:  '.$service_date_disp.' Start Time: '.$start_time.' End Time: '.$end_time.' End Time(eWeb): '.$eweb_end_time.'</br>';
			 	//echo $query1.'</br>';


					$pdf->Cell(10,5,'',0,0,'L');
					$pdf->Cell(40,5,'End Time (S.N.): ',0,0,'L');
					$pdf->SetTextColor(220,50,50);
					$pdf->Cell(20,5,$end_time_frm,1,0,'L');
					$pdf->SetTextColor(0,0,0);
					$pdf->Cell(45,5,'End Time (RSA7a): ',0,0,'L');
					$pdf->SetTextColor(0,0,255);
					$pdf->Cell(30,5,$eweb_end_time_frm,1,1,'L');
					$pdf->SetTextColor(0,0,0);
						

					}

					if ($group_size != $eweb_group_size) {




					$pdf->Cell(10,5,'',0,0,'L');
					$pdf->Cell(40,5,'Group Size (S.N.): ',0,0,'L');
					$pdf->SetTextColor(220,50,50);
					$pdf->Cell(20,5,$group_size,1,0,'L');
					$pdf->SetTextColor(0,0,0);
					$pdf->Cell(45,5,'Group Size (RSA7a): ',0,0,'L');
					$pdf->SetTextColor(0,0,255);
					$pdf->Cell(30,5,$eweb_group_size,1,1,'L');
					$pdf->SetTextColor(0,0,0);



					}




				}




			} // session_exists - end


   		    else {

 
					//================================================= 
					//  Check if Session Not Exists in eWeb for Registrant/Student  
					//================================================= 
				  	
					$query2 = "SELECT  	a.Id as ScheduleId 
								
								/*
									a.StartTime, 
									a.EndTime, 
									DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTimeFrm,
									DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTimeFrm,

									a.SessionGrpSize,
									CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as RegistrantName, 
									CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName,
									b.ExtId as StudentExtId
								*/
					FROM WeeklyServices a,
					     SchStudents b,
					     Registrants c
					WHERE c.Id = '{$RegistrantId}' 
					AND   a.RegistrantId = c.Id
					AND   b.ExtId = '{$student_ext_id}' 

					AND   a.StudentId = b.Id 
					AND   a.ServiceDate = '{$service_date}' 
					AND   a.ScheduleStatusId > 6  

			                ";

 
				$result2 = $connection->query ($query2);

			 	
				if (DB::isError($result2)){
							die("Could not query the database:<br />$query2 ".DB::errorMessage($result1));
				}			
		 	 	
				$eweb_exists =& $result2->numRows();			
		 		
				if ($eweb_exists == 0) { // eweb_exists - start

						$pdf->Ln(2);

						$pdf->Cell(0,5,'_____________________________________________________________________________',0,1,'L');

						$pdf->Cell(20,5,'Provider: ',0,0,'L');
						$pdf->Cell(40,5,$RegistrantNameDisp,0,0,'L');
						$pdf->Cell(20,5,'Student: ',0,0,'L');
						$pdf->Cell(50,5,$StudentNameDisp,0,0,'L');
						$pdf->Cell(15,5,'Date: ',0,0,'L');
						$pdf->Cell(30,5,$service_date_disp,0,1,'L');


						$pdf->Cell(10,5,'',0,0,'L');
						$pdf->Cell(40,5,'Start Time (S.N.): ',0,0,'L');
						$pdf->SetTextColor(220,50,50);
						$pdf->Cell(20,5,$start_time_frm,1,0,'L');
						$pdf->SetTextColor(0,0,0);
						$pdf->Cell(45,5,'Start Time (RSA7a): ',0,0,'L');
						$pdf->SetTextColor(0,0,255);
						$pdf->Cell(30,5,'',1,1,'L');
						$pdf->SetTextColor(0,0,0);


				}	



	 

  		    } 		
	  
		
			$linecount++;
			 
		}	

	}	//read excel - end 
 

	// Delete file in no matched sessions found
	//==========================================

	if ($matched_sessions ==  0) {

	/*	
		unlink($inputFileName);
	
		$query = "DELETE FROM  SchRegistrantSessionNotes 
					WHERE StoredName like '%{$StoredFileName}%' ";
								
		
		$result = $connection->query ($query);

		if (DB::isError($result)){
	                die("Could not query the database:<br />$query ".DB::errorMessage($result));
	    } 

	*/

	} else { // Matched sessions found 

		$query = "UPDATE  SchRegistrantSessionNotes 
						SET OriginalFileName = '{$upload_file_desc}'
					WHERE StoredName like '%{$StoredFileName}%' ";
								
		
		$result = $connection->query ($query);

		if (DB::isError($result)){
	                die("Could not query the database:<br />$query ".DB::errorMessage($result));
	    } 

	}	
 
	

	// Print Found in eWebStaffing - Missing in Session Notes Sessions
	//==================================================================


		$service_start_date = $GLOBALS['ReportFromDate'];
		$service_end_date = $GLOBALS['ReportToDate'];

	 	  	
			$query3 = "SELECT  	a.Id as ScheduleId,
								a.StartTime, 
								a.EndTime, 
								DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTimeFrm,
								DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTimeFrm,
								DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) as ServiceDateDisp, 

								a.SessionGrpSize,
								CONCAT( trim( c.LastName) , ', ', trim(c.FirstName)) as RegistrantName, 
								CONCAT( trim( b.LastName) , ', ', trim(b.FirstName)) as StudentName 

				FROM WeeklyServices a,
				     SchStudents b,
				     Registrants c
				WHERE c.Id = '{$RegistrantId}' 
				AND   a.RegistrantId = c.Id
				AND   a.StudentId = b.Id 
				AND   a.ServiceDate between '{$service_start_date}' AND '{$service_end_date}' 
				AND   a.ScheduleStatusId = 7  
				AND    NOT FIND_IN_SET(a.Id, '{$skip_sessions_list}')
				ORDER BY a.ServiceDate

	                ";

	        //echo $query3.'</br>';        

 
			$result3 = $connection->query ($query3);

		 	
			if (DB::isError($result3)){
						die("Could not query the database:<br />$query3 ".DB::errorMessage($result3));
			}			
	 	 	
			$session_exists =& $result3->numRows();			
	 		
			if ($session_exists > 0) { // 


				while ($row3 =& $result3->fetchRow (DB_FETCHMODE_ASSOC)) {
						$schedule_id = $row3['ScheduleId'];
						$service_date_disp = $row3['ServiceDateDisp'];
						
						$eweb_start_time = $row3['StartTime'];
						$eweb_end_time = $row3['EndTime'];
						
						$eweb_start_time_frm = $row3['StartTimeFrm'];
						$eweb_end_time_frm = $row3['EndTimeFrm'];

						$eweb_group_size = $row3['SessionGrpSize'];
						$registrant_name = $row3['RegistrantName'];
						$student_name = $row3['StudentName'];

					
					$pdf->Ln(2);

		$pdf->Cell(0,5,'_____________________________________________________________________________',0,1,'L');

					$pdf->Cell(20,5,'Provider: ',0,0,'L');
					$pdf->Cell(40,5,$registrant_name,0,0,'L');
					$pdf->Cell(20,5,'Student: ',0,0,'L');
					$pdf->Cell(50,5,$student_name,0,0,'L');
					$pdf->Cell(15,5,'Date: ',0,0,'L');
					$pdf->Cell(30,5,$service_date_disp,0,1,'L');

					$pdf->Cell(10,5,'',0,0,'L');
					$pdf->Cell(40,5,'(2)Start Time (S.N.): ',0,0,'L');
					$pdf->SetTextColor(220,50,50);
					$pdf->Cell(20,5,'',1,0,'L');
					$pdf->SetTextColor(0,0,0);
					$pdf->Cell(45,5,'Start Time (RSA7a): ',0,0,'L');
					$pdf->SetTextColor(0,0,255);
					$pdf->Cell(30,5,$eweb_start_time_frm,1,1,'L');
					$pdf->SetTextColor(0,0,0);


		 

				}  

			}	
	 



	 


 	
	$connection->disconnect();

 	


	$pdf->Ln(5);
	$pdf->Cell(0,5,'_____________________________________________________________________________',0,1,'L');
	$pdf->Cell(10,5,'',0,0,'L');
	$pdf->Cell(50,5,'Total Matched Sessions: ',0,0,'L');
	$pdf->Cell(20,5,$matched_sessions,1,0,'R');


	$pdf->Output();
	
 
	function getMonthStartEndDate($upload_file_desc) {


		$date_arr = explode(" ", $upload_file_desc);

		$from_date = '01 '.$date_arr[0].' '.$date_arr[1];


		$from_date_ufm = strtotime($from_date);
		
		$GLOBALS['ReportFromDate'] = date("Y-m-d",$from_date_ufm);
		$GLOBALS['ReportToDate'] = date("Y-m-t",$from_date_ufm);


	}	

	 
?>