<?php


  require_once("db_GetSetData.php");
  $conn = getCon();


 
  $MandateId = $_POST['MandateId'];  
  $FromDate = $_POST['FromDate'];
  $ToDate = $_POST['ToDate'];


  //$to = '<EMAIL>';
  //$from = '<EMAIL>';
   
  // $from = '<EMAIL>';
  // $to = '<EMAIL>';


  // To send HTML mail, the Content-type header must be set
  $headers  = 'MIME-Version: 1.0' . "\r\n";
  $headers .= 'Content-type: text/html; charset=iso-8859-1' . "\r\n";
   
  // // Create email headers
  // $headers .= 'From: '.$from."\r\n".
  //     'Reply-To: '.$from."\r\n" .
  //     'X-Mailer: PHP/' . phpversion();
   
      
  //================        
  
     $query = "SELECT   DISTINCT      DATE_FORMAT( a.ServiceDate, '%m/%d/%Y' ) AS ServiceDate, 
                        a.ServiceDate as ServiceDateSort,
                        DATE_FORMAT( a.ServiceDate, '%M') AS ApprovalMonthName, 
                        DATE_FORMAT( a.ServiceDate, '%c') AS ApprovalMonth, 
                        DATE_FORMAT( a.ServiceDate, '%Y') AS ApprovalYear, 
                        
                        a.StartTime as StartTimeSort,
                        DATE_FORMAT( a.StartTime, '%l:%i %p' ) as StartTime,
                        DATE_FORMAT( a.EndTime, '%l:%i %p' ) as EndTime,
                        a.SessionGrpSize,
                        concat(' (#s',a.StudentId,'s$)') as StudentId,
                        concat(' (#p',a.RegistrantId,'p$)') as RegistrantId, 
                        concat(b.FirstName,' ',b.LastName) as RegistrantName,
                        concat(c.FirstName,' ',c.LastName) as StudentName,
                        ServiceTypeDesc,
                        GuardianFirstName,
                        GuardianLastName,
                        concat(GuardianFirstName,' ',GuardianLastName) as GuardianName,
                        GuardianEmail,
                        CompanyName,
                        a.MandateId,
                        e.ApprovalEmail,
                        b.Email as RegistrantEmail 

                        

                    FROM     WeeklyServices a,
                             Registrants b,
                             SchStudents c,
                             SchServiceTypes d,
                             Company e
                        WHERE a.MandateId = '{$MandateId}' 
                        AND   a.ScheduleStatusId > '6'
                        AND   a.RegistrantId = b.Id
                        AND   a.StudentId = c.Id
                        AND   a.ServiceTypeId = d.Id
                        AND   GuardianEmail != ''
                        AND   a.ServiceDate between  '{$FromDate}' and '{$ToDate}'   
                ORDER BY ServiceDateSort, StartTimeSort    
            ";

    $result =  mysqli_query($conn, $query) or die
    ("Error in Selecting " . mysqli_error($conn));
   
    //echo '$query: '.$query.'</br>';

    $i = 0;


    while ($row = $result->fetch_assoc()) {
      
    $i++; 



    if ($i == 1) {



      $from = $row['ApprovalEmail'];
      $to = $row['GuardianEmail'];

      if ($row['RegistrantEmail']) {

        $to = $to.';'.$row['RegistrantEmail'];


      }

        //$from = '<EMAIL>';
        //$to = '<EMAIL>';
      
      // Create email headers
      $headers .= 'From: '.$from."\r\n".
          'Reply-To: '.$from."\r\n" .
          'X-Mailer: PHP/' . phpversion();


      // Compose a simple HTML email message
      
      $subject = 'Sessions Approval Request from: '.$row['CompanyName'];
 

      $message = '<html><body>';
      $message .= '<p><b>Child Name:</b> '.$row['StudentName'].'</p>';
      $message .= '<p><b>Provider Name:</b> '.$row['RegistrantName'].'</p>';
      $message .= '<p><b>Sessions Month:</b> '.$row['ApprovalMonthName'].'<b> Year:</b> '.$row['ApprovalYear'].'</p></br><hr>';

      $message .= '<h2>Hi '.$row['GuardianName'].'!</h2></br>';

      $message .= '<p style="font-size:18px;">Please respond to this email with the word  <span style="color: #ff0000">CONFIRMED</span>  in order to verify that following Tele-Therapy Sessions were provided to your child:</p>';

      $message .= '<table width="600" border="1" cellpadding="0" cellspacing="0" bordercolor="#CCCCCC">
            <tr>
              <th>Session Date</th>
              <th>Start Time</th>
              <th>End Time</th>
              <th>Group Size</th>
              <th>Service Type</th>         
              <th>Provider Name</th>
            </tr>';


      $ids = '#d'.$row['MandateId'].'d$-#m'.$row['ApprovalMonth'].'m$-#y'.$row['ApprovalYear'].'y$';
      $ids .='-#n'.$row['GuardianFirstName'].' '.$row['GuardianLastName'].'n$';

    }


      $td = '';
      $td .= '<td>'.$row['ServiceDate'].'</td>';
      $td .= '<td>'.$row['StartTime'].'</td>';
      $td .= '<td>'.$row['EndTime'].'</td>';
      $td .= '<td align="center">'.$row['SessionGrpSize'].'</td>';
      $td .= '<td>'.$row['ServiceTypeDesc'].'</td>';
      $td .= '<td>'.$row['RegistrantName'].'</td>';

      $message .= '<tr>'.$td.'</tr>';  




    } 


    setDisConn($conn);


    //================

  $message .= '</table></br></br><hr>';
  $message .= '<h6>'.$ids.'!</h6>';

  $message .= '</body></html>';
 

  // Sending email
  if(mail($to, $subject, $message, $headers)){
      echo 'Your mail has been sent successfully to: '.$to.'</br></br>';
  } else{
      echo 'Unable to send email. Please try again.';
  }

?>


 