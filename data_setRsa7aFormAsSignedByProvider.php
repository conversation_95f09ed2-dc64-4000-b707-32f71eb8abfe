<?php 

	
	require_once("db_GetSetData.php");

	$conn = getCon();

	
	  
    
	$Data = $_POST['Data'];
	$Data=json_decode($Data,true);


  	foreach ($Data as $FormData) {

	    $FormMandateId = $FormData['FormMandateId'];
	    $FormFromDate = $FormData['FormFromDate'];
	    $FormToDate = $FormData['FormToDate'];
  	    $FormNextStatus = $FormData['FormNextStatus'];
	    $FormProviderName = $FormData['FormProviderName'];

        $FormTypeId = $FormData['FormTypeId'];


		$FormProviderName = mysqli_real_escape_string($conn, $FormProviderName); 
		//$FormProviderTitle = mysqli_real_escape_string($conn, $FormProviderTitle); 

	
		$client_ip_address = getClientIPAddress();


		$query ="INSERT INTO  SchRsa7aFormSignatures
				(
					StatusId,
					MandateId,
					FromDate,
					ToDate,
					ProviderSignatureName,
					ProviderSignatureIPAddress,
					FormTypeId,
					ProviderSignatureTimeStamp
				)
 
				VALUES
				(
					'{$FormNextStatus}',
					'{$FormMandateId}',
					'{$FormFromDate}',
					'{$FormToDate}',
					'{$FormProviderName}',
					'{$client_ip_address}',
					'{$FormTypeId}',
					NOW()
				)";
 	 

		$ret =  setData ($conn, $query);   			

	}	
	
	

	    $url = "https://".$_SERVER['HTTP_HOST'].dirname($_SERVER['SCRIPT_NAME'])."/data_setGenerateApprovalEmail.php?MandateId=".$FormMandateId.'&';
	    $url .= 'FromDate='.$FormFromDate.'&ToDate='.$FormToDate;

	    //echo 'url: '.$url.'</br>'; 

	    $output = file_get_contents($url);



	setDisConn($conn);
	echo $ret;

	function getClientIPAddress() {


		//whether ip is from share internet
		if (!empty($_SERVER['HTTP_CLIENT_IP']))   
		  {
		    $ip_address = $_SERVER['HTTP_CLIENT_IP'];
		  }
		//whether ip is from proxy
		elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))  
		  {
		    $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];
		  }
		//whether ip is from remote address
		else
		  {
		    $ip_address = $_SERVER['REMOTE_ADDR'];
		  }
		return $ip_address;

	}	 

?>

