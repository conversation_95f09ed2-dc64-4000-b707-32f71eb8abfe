<?php  

       
// error_reporting(E_ALL);
// ini_set('display_errors', TRUE);
// ini_set('display_startup_errors', TRUE);
         
  	$SessionNotesFileName = $_POST['SessionNotesFileName'];
  	// 	$SessionNotesFileName = 'hokXG9VQVwoQVfbSIBkv';


  	$UserId = $_POST['UserId'];

  	if (!$UserId) {

		$UserId = 0;  		
  	}


  	require_once("db_GetSetData.php");


  	$err_mesages_arr = array();
  	$x = 0;

 // Verify Group Sessions
 // ================
 
  $conn = getCon();


  $query = "
			SELECT count(*) as grp_cnt,
			        SessionGrpSize, 
					group_concat(  Id SEPARATOR ',' ) as ListOfIds, 
			        SessionDateDisp,
			        SessionDate,
			        StartTime,
			        StartTimeDisp,
			        EndTime,
			         
			        group_concat(  StudentNameDisp SEPARATOR ', ' ) as ListOfStudents,
			        sum(SessionGrpSize)   as MultSessionGrpSize   
			 from SchSessionNotesTrans
			 where SessionNoteFileName = '{$SessionNotesFileName}'
             and  GroupingDesc LIKE 'Group%'
			 group by  SessionDate,
			           StartTime,
			           EndTime
			  -- having   count(*) !=   SessionGrpSize
             having  (( count(*) !=   SessionGrpSize) || ((count(*) * count(*)) != MultSessionGrpSize))
              
		     ";


	  // echo ' group query: '.$query.'<br><br>';	     

     $result =  mysqli_query($conn, $query);
   
     $exlude_ids = '';
 
     while ($row = $result->fetch_assoc()) { // While - Start 

			$err_msg = ' Group Sessions on: '.$row['SessionDateDisp'].' that starts at: '.$row['StartTimeDisp'].' and includes Student(s): '.$row['ListOfStudents'].' were posted with Incorrect Group Size!'; 
			$err_mesages_arr[] = $err_msg;
			//echo $err_msg.'';


			if (!$exlude_ids) {

				$exlude_ids = $row['ListOfIds'];

			} else {


				$exlude_ids = $exlude_ids.','. $row['ListOfIds'];

			}
     
     }	


	setDisConn($conn); 


	if (!$exlude_ids) {

			$exlude_ids = "''";

	}


	// echo 'exlude_ids: '.$exlude_ids.'<br>'; 

 //=================== 	

 $conn = getCon();


  $query = "SELECT  *
     
	FROM
    	SchSessionNotesTrans  
    where  SessionNoteFileName = '{$SessionNotesFileName}'	
    AND Id not in ({$exlude_ids})

    ORDER BY SessionDate, StartTime

     ";

    // echo ' query with exlude_ids: '.$query.'<br><br>'; 

     $result =  mysqli_query($conn, $query) or die
     ("Error in Selecting " . mysqli_error($conn));
   
     $i = 0;
 
     while ($row = $result->fetch_assoc()) { // While - Start 

		 	
     	 $Id = $row['Id'] ;  
     	 $RegistrantId = $row['RegistrantId'] ;  
    	 $StudentExtId = $row['StudentExtId'] ;  
     	 $StudentNameDisp = $row['StudentNameDisp'] ;  
     	 $MandateId = $row['MandateId'] ;  
     	 $SessionDate = $row['SessionDate'] ;  
     	 $SessionDateDisp = $row['SessionDateDisp'] ;  
    	 $StartTime = $row['StartTime'] ;  
    	 $PayrollWeek = $row['PayrollWeek'] ;  
	     $GroupingDesc = $row['GroupingDesc'] ;		
	     $SessionGrpSize = $row['SessionGrpSize'] ;
 
	     $MandSessionFrequency = $row['MandSessionFrequency'] ;
	     $MandSessionLength = $row['MandSessionLength'] ;
	     $MandSessionGrpSize = $row['MandSessionGrpSize'] ;

	     $SessionDeliveryMode = $row['SessionDeliveryMode'] ;


	     $SessionLength = $row['SessionLength'] ;
	     $StartTime = $row['StartTime'] ;
	     $EndTime = $row['EndTime'] ;


	 	/*========= Skip if Student Mandate Exists/Session Date not within Start/End Dates =========*/


		if (($MandateId == '') || ($MandateId == '0') ) {

			$err_msg = ' Active Mandate for Student: '.$StudentNameDisp.' on Session Date: '.$SessionDateDisp.' was NOT FOUND in eWebStaffing!'; 
			$skip = '1';	
					$err_mesages_arr[] = $err_msg;
			//echo $err_msg.'';


			goto skip;

		}


	 	/*========= Skip if Session Already Exists =========*/

		$skip = checkIfSessionAlreadyExists($RegistrantId, $StudentExtId,  $MandateId, $SessionDate, $StartTime, $EndTime); 


		if ($skip == '1') {

			$err_msg 	= ' Session on: '.$SessionDateDisp.' for Student: '.$StudentNameDisp.' Already Exists!'; 
					$err_mesages_arr[] = $err_msg;
			//echo $err_msg.'';


			goto skip;

		}


	 	/*========= Skip if Session Overlaps Existing Student Session =========*/

		$skip = checkSessionsOverlap($RegistrantId, $StudentExtId, $SessionDate, $StartTime); 

		if ($skip == '1') {

			$err_msg = ' 1 Session on: '.$SessionDateDisp.' for Student: '.$StudentNameDisp.' Overlaps Existing Session!'; 
			$err_mesages_arr[] = $err_msg;
			//echo $err_msg.'';


			goto skip;

		}

 
	 	/*========= Skip if Session Frequency is Higher than Mandated =========*/


		// 	$skip = checkSessionFrequency($MandateId, $PayrollWeek, $SessionDate); 


		// if ($skip == '1') {

		// 	$err_msg = ' Session on: '.$SessionDateDisp.' for Student: '.$StudentNameDisp.' posted Frequency which is HIGHER than allowed under the Mandate!'; 
		// 				$err_mesages_arr[] = $err_msg;
		// 	//echo $err_msg.'';


		// 	goto skip;

		// }


	 	/*========= Skip if Session Over Group Limit compared to  Mandated =========*/


		if ($GroupingDesc == 'Group') {

			$skip = checkSessionGroupLimit($RegistrantId, $StudentExtId, $SessionGrpSize); 

			if ($skip == '1') {

				$err_msg = ' Session on: '.$SessionDateDisp.' for Student: '.$StudentNameDisp.' posted Group Size which is HIGHER than allowed under the Mandate!'; 
						$err_mesages_arr[] = $err_msg;
			//echo $err_msg.'';


				goto skip;

			}


		}	

	 	/*========= Skip if Session Duration is Higher than Mandated =========*/

		// $skip = checkSessionDuration($MandateId, $SessionLength); 

	 	if ($SessionLength >  $MandSessionLength) { 

		// if ($skip == '1') {
	 		$skip = '1';	
			$err_msg = ' Session on: '.$SessionDateDisp.' for Student: '.$StudentNameDisp.' posted Duration which is HIGHER than allowed under the Mandate!</br>'; 
				// echo $err_msg.'</br>';
						$err_mesages_arr[] = $err_msg;

			break;

		}


		//====

			$conn1 = getCon();
				
			++$x;	

			$total_hours =  $row['SessionLength'] / 60;
			$total_hours =  number_format($total_hours, 2, '.', ''); 

			$query1 = "INSERT INTO WeeklyServices
						                  (
											PayrollWeek,
											ClientId,
											ClientUnitId,
											ScheduleStatusId,
											ServiceDate,	 
											StartTime, 
											EndTime, 		
											TotalHours , 
											WeekDay,
											 
											RegistrantId, 
											SchoolId,
											StudentId,
											ServiceTypeId,
											MandateId,
											SessionGrpSize,
											SessionDeliveryModeId,
											UserId,
											TransDate )		
											
					    SELECT
									'{$row['PayrollWeek']}',
									a.Id,
									b.Id,
									'8',
									'{$row['SessionDate']}',	 
									'{$row['StartTime']}',	 
                                    '{$row['EndTime']}', 		
                                    '{$total_hours}', 
									substr(dayname('{$SessionDate}'),1,3),
                                    '{$row['RegistrantId']}',
									c.SchoolId,
									c.StudentId,
								  	c.ServiceTypeId,
								  	'{$row['MandateId']}',
								 	'{$row['SessionGrpSize']}',
								 	'{$row['SessionDeliveryMode']}',
									'{$UserId}',
                                     now()	 
                        FROM Clients a, ClientUnits b, SchStudentMandates c             
                        WHERE a.SchoolFL = '1'
                        AND   b.ClientId = a.Id
                        AND   c.RegistrantId != 0
                        AND   c.SchoolId != 0 
                        AND   c.Id =  '{$row['MandateId']}' LIMIT 1
	                ";

			    		

			    		$result1 =  mysqli_query($conn1, $query1);	

			    		setDisConn($conn1); 

	    //====	
        skip:	

     } // while - end
	 



    
    //=================== 
	function checkIfSessionAlreadyExists($RegistrantId, $StudentExtId, $MandateId, $SessionDate, $StartTime, $EndTime) 
		{


		$conn1 = getCon();


		 $query = " SELECT 1 
		 		 FROM WeeklyServices a, SchStudents b, SchStudentMandates c
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   b.ExtId = '{$StudentExtId}' 
					AND   a.StudentId = b.Id 
					AND   a.StudentId = c.StudentId 
					AND   a.MandateId = '{$MandateId}' 
	/*
					AND   c.SchoolId != 0 
					AND   a.ServiceDate = '{$SessionDate}' 
					AND   a.StartTime = '{$StartTime}' 
					AND   a.EndTime = '{$EndTime}' 
	*/				
					AND   a.ScheduleStatusId >= 7  		 "; 	


 
 		$result1 =  mysqli_query($conn1, $query);	
	 
 
		$session_exists=mysqli_num_rows($result1);
  

 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}


 		setDisConn($conn1); 


	}

	function checkSessionsOverlap($RegistrantId, $StudentExtId, $SessionDate, $StartTime)
	{


		$conn1 = getCon();

		// $query = " SELECT a.* FROM WeeklyServices a, SchStudents b
		// 					WHERE a.RegistrantId != '{$RegistrantId}' 
		// 					AND   b.ExtId = '{$StudentExtId}' 
		// 					AND   a.StudentId = b.Id 
		// 					AND   a.ServiceDate = '{$SessionDate}'
		// 					AND   (('{$StartTime}' >  a.StartTime) && ('{$StartTime}' < a.EndTime) ) 
		// 					AND   a.ScheduleStatusId >= 7  		 "; 	
 		

		$query = " SELECT 1 FROM WeeklyServices a 
					WHERE a.RegistrantId = '{$RegistrantId}' 
					AND   a.ServiceDate = '{$SessionDate}'
					AND   (('{$StartTime}' >=   a.StartTime) && ('{$StartTime}' < a.EndTime) ) 
					AND   a.ScheduleStatusId >= 7  
                    -- AND   a.SessionGrpSize = '1'	

                    	 "; 	


		// echo 'overlap query: '.$query.'<br><br>';			


 		$result1 =  mysqli_query($conn1, $query);	
	 	
 
		$session_exists=mysqli_num_rows($result1);
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}


 		setDisConn($conn1); 

	}

	function checkSessionFrequency($MandateId, $PayrollWeek,$SessionDate)
	{

		$conn1 = getCon();


		$RemaininqFreq = 0; 


		$query = " call  proc_getSchStudentMandateRemainingFreq ('{$MandateId}', '{$PayrollWeek}', '{$SessionDate}')";

			 // $query = " SELECT  a.SessionFrequency,
			 //                (SELECT count(*) 
    //                               FROM WeeklyServices b
    //                               where b.MandateId = '{$MandateId}'
    //                               and b.ScheduleStatusId > 6
    //                               and b.PayrollWeek = '{$PayrollWeek}'
    //                           ) as RemaininqFreq  
 
    //           			 FROM SchStudentMandates a
    //            			WHERE a.Id = '{$MandateId}'       
				// 		"; 	

 
		 
						
 		$result1 =  mysqli_query($conn1, $query);	

  	 	

	   while ($row1 = $result1->fetch_assoc()) { // While - Start 
		
				$AvailableFreq = $row1['AvailableFreq'];
		
		}		



 		if ($AvailableFreq <= 0) {


 			return '1';

 		} else {

 			return '0';

 		}

 		setDisConn($conn1); 


	}  

	function checkSessionGroupLimit($RegistrantId, $StudentExtId, $SessionGrpSize)

	{

			 $conn1 = getCon();

			 $query = " SELECT 1 FROM SchStudentMandates a, SchStudents b
						WHERE a.RegistrantId = '{$RegistrantId}' 
						AND   b.ExtId = '{$StudentExtId}' 
						AND   a.StudentId = b.Id 
						AND   '{$SessionGrpSize}' > a.SessionGrpSize 
						AND   a.StatusId = '1'  
						AND   a.SchoolId != 0 
						AND   a.SessionGrpSize > 1
                        AND   a.Id = (select c.Id FROM  SchStudentMandates c
										where a.RegistrantId = c.RegistrantId 
						                AND   a.StudentId = c.StudentId  
                                        AND   c.SessionGrpSize > 1 
                                        ORDER BY c.SessionGrpSize DESC LIMIT 1
									
                        )						
 						"; 	


 		$result1 =  mysqli_query($conn1, $query);	
	 	
 
		$session_exists=mysqli_num_rows($result1);
 		
 		if ($session_exists > 0) {

 			return '1';

 		} else {

 			return '0';

 		}


 		setDisConn($conn1); 



	} 

	// function checkSessionDuration($MandateId, $SessionLength)
	//  {



	// 	 $query = " SELECT 1 FROM SchStudentMandates  
	// 				WHERE Id = '{$MandateId}' 
	// 				AND   '{$SessionLength}' > SessionLength
	// 				AND   StatusId = '1' 	 "; 	

  	// 	$result1 =  mysqli_query($conn1, $query);	
	 	
 
	// 	$session_exists=mysqli_num_rows($result1);
 		
 	// 	if ($session_exists > 0) {

 	// 		return '1';

 	// 	} else {

 	// 		return '0';

 	// 	}


 	// 	setDisConn($conn1); 



	// } 



	setDisConn($conn); 


  // Delete selected Session Notes transactions
  //======================================



  //======================================

	$err_mesages_arr[] = '# of Uploaded Sessions: '.$x;


 	$ret_messages = json_encode($err_mesages_arr);
 	echo $ret_messages;

	// echo json_encode($err_mesages_arr);
 	 
?>