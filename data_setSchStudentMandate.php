<?php 



	require_once("db_GetSetData.php"); 	

	$conn = getCon();


	$MandateId = $_POST['MandateId'];
	$StatusId = $_POST['StatusId'];
	$PlaceOfService = $_POST['PlaceOfService'];
	$StartDate = $_POST['StartDate'];	
	$EndDate = $_POST['EndDate'];
	$SchoolId = $_POST['SchoolId'];
	$RegistrantId = $_POST['RegistrantId'];
	$SpecialPayRate = $_POST['SpecialPayRate'];
	$UserId = $_POST['UserId'];

	// /* Update Mandate Info
	//  =======================*/

			
    $query ="UPDATE SchStudentMandates 
				SET StatusId = '{$StatusId}',
				    PlaceOfService = '{$PlaceOfService}', 
					StartDate = '{$StartDate}',
					EndDate = '{$EndDate}',
					SchoolId = '{$SchoolId}',
					RegistrantId = '{$RegistrantId}',
					SpecialPayRate = '{$SpecialPayRate}',
				    UserId = '{$UserId}',
					TransDate = now()
			WHERE 	Id = '{$MandateId}' 
				";


	$ret =  setData ($conn, $query);   			

	setDisConn($conn);


	$conn = getCon();

	// /* Update Student's School from Assigned Mandate 
	//  ===================================================*/

    $query1 ="  UPDATE SchStudents a, SchStudentMandates b 
					SET a.SchoolId = '{$SchoolId}',
					    a.UserId = '{$UserId}',
						a.TransDate = now()
				WHERE 	b.Id = '{$MandateId}'
				AND     a.Id = b.StudentId 
				";

	$ret =  setData ($conn, $query1);   			
	setDisConn($conn);
	//echo $ret;
	echo $query;

	
 
?>
