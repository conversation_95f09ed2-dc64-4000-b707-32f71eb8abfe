<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$RegistrantId = $_GET['RegistrantId'];  
	$SessionDate = $_GET['SessionDate'];
 

    $query = "	SELECT  Id as SessionId,
                        StartTime,
                        EndTime,
                        DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTimeDisp,  
                        DATE_FORMAT( EndTime, '%l:%i %p' ) EndTimeDisp 
                   FROM  WeeklyServices   
                        WHERE RegistrantId = '{$RegistrantId}' 
                        AND   ServiceDate = '{$SessionDate}'  
                        AND   ScheduleStatusId > 6
  
         

                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

