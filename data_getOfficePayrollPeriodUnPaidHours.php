<?php 
	
  require_once("db_GetSetData.php");

	$conn = getCon();

	$FromDate = $_GET['FromDate'];
	$ToDate = $_GET['ToDate'];


    $query = "	SELECT          b.OfficeEmployeeId,
                  CONCAT( trim( a.LastName) , ', ', trim(a.FirstName)) as EmployeeName,
                  DATE_FORMAT( b.WorkDate, '%m-%d-%Y' ) AS WorkDate,
                  b.WorkDate as WorkDateUnf,
                  a.PayRate,
                  a.PayrollId,
          sum(TotalHours) as TotalHours 

         from   OfficeEmployees a,
                OfficeEmployeesPayrollRecords b 
               Where a.Id = b.OfficeEmployeeId
                 AND   b.PaidFL = 0
                 AND   b.WorkDate between '{$FromDate}' AND '{$ToDate}' 
                 group by b.OfficeEmployeeId, b.WorkDate
                    ";

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;
?>

