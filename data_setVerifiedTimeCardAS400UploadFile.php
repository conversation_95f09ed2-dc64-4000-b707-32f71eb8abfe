<?PHP


	$UserId = $_POST['UserId'];
	if (!$UserId) {
		$UserId = '0';
	}



	//AS400 Input File  
	//==========================================================

	$out_File = "../uploads/eweb_timecards_to_400.txt";
	$fh = fopen($out_File, 'w') or die("can't open file");

	$path = "/usr/share/php/libzend-framework-php";
    set_include_path(get_include_path() . PATH_SEPARATOR . $path);

	require_once('DB.php');
	include('db_login.php');
	
	$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    if (DB::isError($connection)){
		$connection = DB::connect("mysql://$db_username:$db_password@$db_host/$db_database");
    }

	
	if (DB::isError($connection)){
		die("Could not connect to the database: <br />".DB::errorMessage($connection));
    } 

	
	$query = "SELECT 	SUBSTRING_INDEX(c.ExtId, '#', 1 ) as BillingClientCode,
						SUBSTRING_INDEX( c.ExtId, '#', -1 ) as BillingClientArea,
						d.ExtId as OverrideBillingCode,
						a.ClientUnitId,
						b.ExtId as EmplId,
						b.LastName,
						b.FirstName,
						DATE_FORMAT( ServiceDate, '%m%d%Y' ) as ServiceDate,
						DATE_FORMAT( StartTime, '%h%i%p' ) as StartTime, 
						DATE_FORMAT( EndTime, '%h%i%p' ) as EndTime,
						ABS(TotalHours) as TotalHours
						
				FROM 	WeeklyServices a, 
						Registrants b,
						ClientUnits c,
						ServiceTypes d
			WHERE 	a.ScheduleStatusId = 8
					AND a.PayrollBatchNumber = ''
					AND a.ClientUnitId = c.Id
					AND a.RegistrantId  = b.Id
					AND c.ExtId != ''
					AND a.ServiceTypeId = d.Id 
					";
	




	$result = $connection->query ($query);

	while ($row =& $result->fetchRow (DB_FETCHMODE_ASSOC)) {	

			$count++;

			//====================================
			// Client Code
			//====================================

			$client = $row['BillingClientCode'];
			

			//====================================
			// Department Code
			//====================================
			

			$dept_long = '';		


			if ($row['OverrideBillingCode'] != '') {

				$dept_long = $row['OverrideBillingCode']; 	
				
			} else {

				$dept_long = $row['BillingClientArea']; 	

			} 


			$dept = substr($dept_long,0,6);
			//====================================
			// Service Date
			//====================================
			
			$serv_date = $row['ServiceDate'];
		 
			//====================================
			// Shift Code
			//====================================
			
			$start_time = $row['StartTime'];
			
			$date_arr = date_parse_from_format("g:i A", $start_time); 

			   switch ($date_arr[hour] ) {  		  
	  			 
					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "23") and ($date_arr[hour] <= "24")):  
					$shift_code = "1";   
					break;

					// Employee Shift - 1 (Night)
					case (($date_arr[hour] >= "0") and ($date_arr[hour] < "7")):  
					$shift_code = "1";   
					break;
					
					// Employee Shift - 2 (Day)
					case (($date_arr[hour] >= "7") and ($date_arr[hour] < "15")):  
					$shift_code = "2";   
					break;

		        
					// Employee Shift - 3 (Evening)
					case (($date_arr[hour] >= "15") and ($date_arr[hour] < "23")):  
					$shift_code = "3";   
					break; 
					
					
				} 

			//====================================
			// Start Time 
			//====================================
			
			$StartTime = $row['StartTime']; 
			$StartTime = substr($StartTime,0, 5); 
			//====================================
			// End Time 
			//====================================
			
			$EndTime = $row['EndTime']; 
			$EndTime = substr($EndTime,0, 5);
			//====================================
			// Total Hours 
			//====================================
			
			$hours_worked = $row['TotalHours']; 
				
			//====================================
			// Employee ID 
			//====================================
			
			$emplid = $row['EmplId']; 
			
			//====================================
			// Patient ID 
			//====================================
			
			$patientid = $row['PatientId']; 

			//====================================
			// Service Type ID 
			//====================================
			
			$service_type = $row['ScheduleTypeId '];


			
			//==============================================				
			//=====================================

				$client = str_pad($client,6, ' ', STR_PAD_RIGHT);
				$dept = str_pad($dept,6, ' ', STR_PAD_RIGHT);
				$name = str_pad($line_of_text[1],20, ' ', STR_PAD_RIGHT);
				$serv_date = str_pad($serv_date,10, ' ', STR_PAD_RIGHT);
				$emplid = str_pad($emplid,9, '0', STR_PAD_LEFT);
				$patientid = str_pad($patientid,6, '0', STR_PAD_LEFT);
				$StartTime = str_replace(":", "", $StartTime);
				$StartTime = str_pad($StartTime,4, '0', STR_PAD_LEFT);
				$EndTime = str_replace(":", "", $EndTime);
				$EndTime = str_pad($EndTime,4, '0', STR_PAD_LEFT);
				$shift_code = str_pad($shift_code,2, ' ', STR_PAD_RIGHT);
				$hours_worked = sprintf("%01.2f", $hours_worked);
				$hours_worked = str_replace(".", "", $hours_worked);
				$hours_worked = str_pad($hours_worked,4, '0', STR_PAD_LEFT);

				
				/*echo (' Client: '.$client);
				echo (' Dept: '.$dept);
				//echo (' Last Name: '.$name);
				echo (' Id: '.$emplid);
				echo (' Date Worked: '.$serv_date);
				echo (' Start Time: '.$start_time);
				echo (' Start Hour: '.$date_arr[hour]);
				echo (' Shift Code: '.$shift_code);
				echo (' Hours Worked: '.$hours_worked.'</br>');
				*/
				
				$out_Line = $client." ".$dept." ".$emplid." ".$serv_date." ".$shift_code." ".$StartTime." ".$EndTime." ".$hours_worked." ".$patientid." EOL"."\n";
				fwrite($fh, $out_Line);
				//echo $out_Line.'</br>';	

				//$client = '';
				//$dept = '';

				//======================================
			
 
	}
	

	  
	fclose($file_handle);
	fclose($fh);

	/* Download Create File
	 ================================*/
/*
	header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename='.basename($out_File));
    header('Content-Transfer-Encoding: binary');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($out_File));
    ob_clean();
    flush();
    readfile($out_File);
*/
	

	//================================
	// Get Next "Payroll Batch Number"
	//==================================
	 
	$next_payroll_batch_num = 0; 
	

	$query1 = "SELECT (MAX(Id) + 1) as NextBatchNumber 
				FROM PayrollBatchHeader";
	
	$result1 = $connection->query ($query1);
 	
 	while ($row1 =& $result1->fetchRow (DB_FETCHMODE_ASSOC)) {	

 		$next_payroll_batch_num = $row1['NextBatchNumber']; 
 	}	
	


	//================================
	// Add "PayrollBatchHeader" Record
	//==================================
	 
	 

	$query2 = "INSERT INTO PayrollBatchHeader
				(	Id,
					BatchDate,
					BatchCount,
					UserId,
					TransDate)
				VALUES
				(
					'{$next_payroll_batch_num}',
					curdate(),
					'{$count}',
					'{$UserId}',
					NOW()
				);";
	
	$result2 = $connection->query ($query2);
	 
	echo '$query2: '.$query2;

	//================================
	// Update "Billing Extract Date"
	//==================================
	 
	
	$query3 = "UPDATE WeeklyServices a, ClientUnits c
				  SET a.PayrollBatchNumber ='{$next_payroll_batch_num}'
			WHERE a.PayrollBatchNumber = ''
			AND   a.ScheduleStatusid = 8
			AND   a.ClientUnitId = c.Id
			AND   c.ExtId != ''
			";
	
	$result3 = $connection->query ($query3);
 	 
	
	$connection->disconnect();
	
	

	
?>