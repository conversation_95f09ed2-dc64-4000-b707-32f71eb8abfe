<?php 

	require_once("db_GetSetData.php");

	$WorkDate = $_GET['WorkDate'];  
	$OfficeEmployeeId = $_GET['OfficeEmployeeId'];  


	$conn = getCon();

    $query = "	 SELECT Id as PayrollRecordId,
    					Workdate,
    					DATE_FORMAT(  WorkDate, '%m-%d-%Y' ) AS WorkDate, 
						DATE_FORMAT( StartTime, '%l:%i %p' ) as StartTime,
						DATE_FORMAT( EndTime, '%l:%i %p' ) as EndTime,
						TotalHours  

				 from    OfficeEmployeesPayrollRecords  
				    WHERE OfficeEmployeeId = '{$OfficeEmployeeId}'  
				    AND WorkDate = '{$WorkDate}'
                    ANd ClockInTime != '00:00:00' 
				    AND ClockOutTime != '00:00:00'   

			"; 

	$ret = getData ($conn, $query);
	setDisConn($conn);

	echo $ret;	  

?>
